* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
    direction: rtl;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 30px;
    align-items: start;
}

.activation-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    animation: slideUp 0.6s ease-out;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 40px;
    text-align: center;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.logo i {
    font-size: 2.5rem;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
}

.header h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.header p {
    opacity: 0.9;
    font-size: 1.1rem;
}

.activation-form {
    padding: 40px;
}

.form-group {
    margin-bottom: 25px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

label {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 1rem;
}

label i {
    color: #4facfe;
    width: 20px;
}

input, select {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

input:focus, select:focus {
    outline: none;
    border-color: #4facfe;
    background: white;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

input::placeholder {
    color: #adb5bd;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 5px;
    display: block;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.error-message.show {
    opacity: 1;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 40px;
}

.btn-primary, .btn-secondary {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: 'Cairo', sans-serif;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    flex: 1;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    flex: 0.5;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.status-panel {
    margin-top: 30px;
    padding: 20px;
    border-radius: 10px;
    display: none;
}

.status-panel.show {
    display: block;
    animation: fadeIn 0.3s ease;
}

.loading {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #4facfe;
    font-weight: 600;
}

.success-message {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #28a745;
    font-weight: 600;
    background: #d4edda;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #c3e6cb;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 15px;
    color: #dc3545;
    font-weight: 600;
    background: #f8d7da;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #f5c6cb;
}

.info-panel {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: fit-content;
    animation: slideUp 0.6s ease-out 0.2s both;
}

.info-panel h3 {
    color: #333;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.info-panel h3 i {
    color: #4facfe;
}

.info-panel ul {
    list-style: none;
}

.info-panel li {
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
    color: #666;
    position: relative;
    padding-right: 25px;
}

.info-panel li:before {
    content: '✓';
    position: absolute;
    right: 0;
    color: #28a745;
    font-weight: bold;
}

.info-panel li:last-child {
    border-bottom: none;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .activation-form {
        padding: 30px 20px;
    }
    
    .header {
        padding: 30px 20px;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn-secondary {
        flex: 1;
    }
}

.input-valid {
    border-color: #28a745 !important;
    background: #f8fff9 !important;
}

.input-invalid {
    border-color: #dc3545 !important;
    background: #fff8f8 !important;
}

.fade-out {
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.fade-in {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}
