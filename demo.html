<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض توضيحي - نظام تفعيل IAS</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .demo-banner {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px;
            text-align: center;
            margin-bottom: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.3);
        }
        
        .demo-banner h3 {
            margin: 0;
            font-size: 1.2rem;
        }
        
        .demo-banner p {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        
        .demo-controls {
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .demo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online {
            background: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .feature-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #4facfe;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature-desc {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-banner">
            <h3><i class="fas fa-flask"></i> عرض توضيحي - نظام تفعيل IAS</h3>
            <p>هذا عرض توضيحي للنظام. في البيئة الحقيقية، سيتم الاتصال بقاعدة بيانات Oracle.</p>
        </div>
        
        <div class="demo-controls">
            <h4><i class="fas fa-cogs"></i> أدوات التحكم التجريبية</h4>
            <button class="demo-btn" onclick="fillDemoData()">
                <i class="fas fa-fill-drip"></i> ملء بيانات تجريبية
            </button>
            <button class="demo-btn" onclick="simulateSuccess()">
                <i class="fas fa-check-circle"></i> محاكاة تفعيل ناجح
            </button>
            <button class="demo-btn" onclick="simulateError()">
                <i class="fas fa-exclamation-triangle"></i> محاكاة خطأ
            </button>
            <button class="demo-btn" onclick="resetForm()">
                <i class="fas fa-undo"></i> إعادة تعيين
            </button>
            
            <div style="margin-top: 15px;">
                <strong>حالة النظام:</strong>
                <span class="status-indicator status-online"></span>
                <span>متصل (وضع تجريبي)</span>
            </div>
        </div>
        
        <div class="activation-card">
            <div class="header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>IAS System</h1>
                </div>
                <h2>تفعيل النظام</h2>
                <p>يرجى إدخال بيانات التفعيل للمتابعة</p>
            </div>

            <form id="activationForm" class="activation-form">
                <div class="form-group">
                    <label for="token">
                        <i class="fas fa-key"></i>
                        رمز التفعيل (Token)
                    </label>
                    <input type="text" id="token" name="token" required 
                           placeholder="أدخل رمز التفعيل" maxlength="100">
                    <span class="error-message" id="token-error"></span>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="serverId1">
                            <i class="fas fa-server"></i>
                            معرف الخادم الأول
                        </label>
                        <input type="text" id="serverId1" name="serverId1" required 
                               placeholder="معرف الخادم 1" maxlength="50">
                        <span class="error-message" id="serverId1-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="serverId2">
                            <i class="fas fa-server"></i>
                            معرف الخادم الثاني
                        </label>
                        <input type="text" id="serverId2" name="serverId2" required 
                               placeholder="معرف الخادم 2" maxlength="50">
                        <span class="error-message" id="serverId2-error"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="clientName">
                            <i class="fas fa-user"></i>
                            اسم العميل
                        </label>
                        <input type="text" id="clientName" name="clientName" required 
                               placeholder="اسم العميل" maxlength="100">
                        <span class="error-message" id="clientName-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="clientUsername">
                            <i class="fas fa-user-tag"></i>
                            معرف المستخدم
                        </label>
                        <input type="text" id="clientUsername" name="clientUsername" required 
                               placeholder="معرف المستخدم" maxlength="50">
                        <span class="error-message" id="clientUsername-error"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="sourceUsername">
                        <i class="fas fa-database"></i>
                        مصدر البيانات
                    </label>
                    <input type="text" id="sourceUsername" name="sourceUsername" required 
                           placeholder="مصدر البيانات" maxlength="100">
                    <span class="error-message" id="sourceUsername-error"></span>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="licenseString">
                            <i class="fas fa-certificate"></i>
                            نص الترخيص
                        </label>
                        <input type="text" id="licenseString" name="licenseString" 
                               placeholder="نص الترخيص (اختياري)" maxlength="200">
                        <span class="error-message" id="licenseString-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="licenseNumber">
                            <i class="fas fa-hashtag"></i>
                            رقم الترخيص
                        </label>
                        <input type="text" id="licenseNumber" name="licenseNumber" required 
                               placeholder="رقم الترخيص" maxlength="20">
                        <span class="error-message" id="licenseNumber-error"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="expiryDate">
                            <i class="fas fa-calendar-alt"></i>
                            تاريخ الانتهاء
                        </label>
                        <input type="date" id="expiryDate" name="expiryDate">
                        <span class="error-message" id="expiryDate-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="moduleString">
                            <i class="fas fa-puzzle-piece"></i>
                            نص الوحدة
                        </label>
                        <input type="text" id="moduleString" name="moduleString" 
                               placeholder="نص الوحدة" maxlength="100">
                        <span class="error-message" id="moduleString-error"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="serverType">
                        <i class="fas fa-cogs"></i>
                        نوع الخادم
                    </label>
                    <select id="serverType" name="serverType" required>
                        <option value="SERVER">خادم (SERVER)</option>
                        <option value="CLIENT">عميل (CLIENT)</option>
                        <option value="ADMIN">مدير (ADMIN)</option>
                    </select>
                    <span class="error-message" id="serverType-error"></span>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary" id="activateBtn">
                        <i class="fas fa-rocket"></i>
                        تفعيل النظام
                    </button>
                    <button type="reset" class="btn-secondary">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </form>

            <div class="status-panel" id="statusPanel">
                <div class="status-content">
                    <div class="loading" id="loadingSpinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>جاري التفعيل...</span>
                    </div>
                    <div class="success-message" id="successMessage">
                        <i class="fas fa-check-circle"></i>
                        <span>تم تفعيل النظام بنجاح!</span>
                    </div>
                    <div class="error-message" id="errorMessage">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="errorText">حدث خطأ أثناء التفعيل</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h3><i class="fas fa-info-circle"></i> معلومات مهمة</h3>
            <ul>
                <li>تأكد من صحة جميع البيانات المدخلة</li>
                <li>رمز التفعيل حساس لحالة الأحرف</li>
                <li>يجب أن يكون الخادم متصلاً بقاعدة البيانات</li>
                <li>في حالة وجود مشاكل، تواصل مع الدعم الفني</li>
            </ul>
        </div>
    </div>

    <div class="feature-grid">
        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-shield-alt"></i>
            </div>
            <div class="feature-title">أمان متقدم</div>
            <div class="feature-desc">
                تشفير البيانات باستخدام AES-256 وحماية من الهجمات الأمنية
            </div>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-database"></i>
            </div>
            <div class="feature-title">تكامل Oracle</div>
            <div class="feature-desc">
                اتصال مباشر مع قاعدة بيانات Oracle وتنفيذ الإجراءات المخزنة
            </div>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-mobile-alt"></i>
            </div>
            <div class="feature-title">تصميم متجاوب</div>
            <div class="feature-desc">
                واجهة تعمل بسلاسة على جميع الأجهزة والشاشات
            </div>
        </div>

        <div class="feature-card">
            <div class="feature-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="feature-title">تتبع العمليات</div>
            <div class="feature-desc">
                نظام سجلات شامل لتتبع جميع عمليات التفعيل والأخطاء
            </div>
        </div>
    </div>

    <script src="demo.js"></script>
</body>
</html>
