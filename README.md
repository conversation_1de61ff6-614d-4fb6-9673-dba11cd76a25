# نظام تفعيل IAS - IAS Activation System

نظام تفعيل متقدم مطور خصيصاً للعمل مع Oracle PL/SQL package `IAS_SYS.INIT_PKG_NEW`.

## المميزات الرئيسية

- 🔐 **نظام تفعيل آمن** مع تشفير البيانات
- 🌐 **واجهة ويب احترافية** باللغة العربية
- 🛡️ **حماية متقدمة** ضد XSS و SQL Injection
- 📊 **نظام سجلات شامل** لتتبع العمليات
- 🔄 **دعم أنواع مختلفة من التراخيص**
- ⚡ **أداء عالي** مع تحسينات للسرعة

## متطلبات النظام

### الخادم
- PHP 7.4 أو أحدث
- Oracle Database 11g أو أحدث
- امتداد PHP OCI8
- امتداد PHP OpenSSL
- امتداد PHP JSON

### المتصفح
- متصفح حديث يدعم HTML5 و CSS3
- JavaScript مفعل

## التثبيت والإعداد

### 1. إعداد قاعدة البيانات

```sql
-- تأكد من وجود المستخدم IAS_SYS
CREATE USER IAS_SYS IDENTIFIED BY your_password;
GRANT CONNECT, RESOURCE TO IAS_SYS;
GRANT CREATE SESSION TO IAS_SYS;

-- تثبيت الباكيج من ملف init.txt
@init.txt
```

### 2. إعداد الملفات

```bash
# نسخ الملفات إلى مجلد الويب
cp -r * /var/www/html/ias-activation/

# إنشاء مجلد السجلات
mkdir -p /var/www/html/ias-activation/api/logs
chmod 755 /var/www/html/ias-activation/api/logs
```

### 3. تكوين النظام

قم بتعديل ملف `api/config.php`:

```php
// إعدادات قاعدة البيانات
define('DB_HOST', 'your_oracle_host');
define('DB_PORT', '1521');
define('DB_SERVICE_NAME', 'your_service_name');
define('DB_USERNAME', 'IAS_SYS');
define('DB_PASSWORD', 'your_password');

// مفتاح التشفير (يجب تغييره)
define('ENCRYPTION_KEY', 'your_unique_encryption_key_here');
```

## الاستخدام

### 1. فتح شاشة التفعيل

افتح المتصفح وانتقل إلى:
```
http://your-server/ias-activation/activation.html
```

### 2. إدخال بيانات التفعيل

املأ الحقول التالية:

- **رمز التفعيل**: الرمز المميز للنظام
- **معرف الخادم الأول**: معرف فريد للخادم
- **معرف الخادم الثاني**: معرف إضافي للخادم
- **اسم العميل**: اسم الشركة أو العميل
- **معرف المستخدم**: اسم المستخدم الرئيسي
- **مصدر البيانات**: مصدر قاعدة البيانات
- **رقم الترخيص**: رقم مكون من 18 رقم
- **نوع الخادم**: اختر من (خادم، عميل، مدير)

### 3. التفعيل

اضغط على زر "تفعيل النظام" وانتظر النتيجة.

## أنواع التراخيص

| النوع | الوصف | المدة |
|-------|--------|-------|
| 1 | مستخدم واحد | غير محدود |
| 2 | متعدد المستخدمين | غير محدود |
| 3 | محدود بالوقت | حسب التاريخ |
| 4 | غير محدود | دائم |
| 5 | تجريبي | محدود بعدد المرات |
| 6 | ترقية | حسب الإعدادات |
| 7 | مؤسسي | متقدم |
| 8 | مطور | للتطوير |

## API المتاحة

### تفعيل النظام
```
POST /api/activate.php
Content-Type: application/json

{
  "token": "IAS_SYS_TOKEN_2024",
  "serverId1": "SERVER_001",
  "serverId2": "SERVER_002",
  "clientName": "شركة التقنية",
  "clientUsername": "admin",
  "sourceUsername": "oracle_db",
  "licenseNumber": "123456789012345678",
  "serverType": "SERVER"
}
```

### الاستجابة
```json
{
  "success": true,
  "message": "تم تفعيل النظام بنجاح",
  "data": {
    "activation_id": "12345",
    "status": "activated",
    "type": "SERVER",
    "activated_date": "2024-01-15 10:30:00"
  },
  "timestamp": "2024-01-15 10:30:00"
}
```

## الأمان

### التشفير
- استخدام AES-256-CBC لتشفير البيانات الحساسة
- مفاتيح تشفير فريدة لكل تثبيت
- تشفير كلمات المرور باستخدام SHA-256

### الحماية
- حماية من XSS attacks
- حماية من SQL Injection
- Rate limiting للطلبات
- CSRF token protection
- Session security

### السجلات
- تسجيل جميع محاولات التفعيل
- تتبع الأخطاء والتحذيرات
- دوران السجلات التلقائي

## الاختبار

### تشغيل الاختبارات
```bash
# اختبار من سطر الأوامر
php api/test_activation.php

# أو من المتصفح
http://your-server/ias-activation/api/test_activation.php
```

### اختبارات متاحة
- ✅ التحقق من صحة البيانات
- ✅ اختبار البيانات غير الصحيحة
- ✅ اختبار الاتصال بقاعدة البيانات
- ✅ اختبار ميزات الأمان
- ✅ اختبار التفعيل المسبق

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
```
الحل: تحقق من إعدادات قاعدة البيانات في config.php
```

#### رمز التفعيل غير صحيح
```
الحل: تأكد من صحة الرمز وأنه يطابق النظام
```

#### انتهاء صلاحية الترخيص
```
الحل: تحديث تاريخ انتهاء الترخيص
```

### ملفات السجلات
```bash
# عرض السجلات
tail -f api/logs/activation.log

# البحث في السجلات
grep "ERROR" api/logs/activation.log
```

## التطوير

### إضافة ميزات جديدة
1. قم بتعديل ملف `activation.js` للواجهة
2. أضف API endpoints في `api/activate.php`
3. حدث قاعدة البيانات حسب الحاجة

### المساهمة
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة التحسينات
4. إرسال Pull Request

## الدعم الفني

للحصول على الدعم الفني:
- 📧 البريد الإلكتروني: <EMAIL>
- 📞 الهاتف: +966-XX-XXXXXXX
- 🌐 الموقع: https://ias-system.com

## الترخيص

هذا النظام مطور خصيصاً لنظام IAS ومحمي بحقوق الطبع والنشر.

---

**تم التطوير بواسطة**: فريق IAS التقني  
**الإصدار**: 1.0.0  
**تاريخ التحديث**: 2024-01-15
