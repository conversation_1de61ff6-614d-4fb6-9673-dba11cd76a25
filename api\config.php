<?php
/**
 * IAS System Configuration File
 * تكوين نظام IAS
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '1521');
define('DB_SERVICE_NAME', 'orcl'); // أو اسم الخدمة الخاص بك
define('DB_USERNAME', 'IAS_SYS');
define('DB_PASSWORD', 'ys123');
define('DB_CHARSET', 'UTF8');

// Application Configuration
define('APP_NAME', 'IAS Activation System');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', true); // تعيين إلى false في الإنتاج

// Security Configuration
define('ENCRYPTION_KEY', 'IAS_SYS_2024_SECURE_KEY'); // مفتاح التشفير
define('SESSION_TIMEOUT', 3600); // مهلة الجلسة بالثواني
define('MAX_LOGIN_ATTEMPTS', 5); // عدد محاولات تسجيل الدخول القصوى

// Logging Configuration
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('LOG_FILE', __DIR__ . '/logs/activation.log');
define('LOG_MAX_SIZE', 10 * 1024 * 1024); // 10MB

// API Configuration
define('API_TIMEOUT', 30); // مهلة API بالثواني
define('API_RATE_LIMIT', 100); // عدد الطلبات المسموحة في الساعة
define('API_VERSION', 'v1');

// License Types
define('LICENSE_TYPES', [
    1 => 'SINGLE_USER',
    2 => 'MULTI_USER', 
    3 => 'TIME_LIMITED',
    4 => 'UNLIMITED',
    5 => 'TRIAL',
    6 => 'UPGRADE',
    7 => 'ENTERPRISE',
    8 => 'DEVELOPER'
]);

// Server Types
define('SERVER_TYPES', [
    'SERVER' => 'خادم رئيسي',
    'CLIENT' => 'عميل',
    'ADMIN' => 'مدير النظام',
    'PENDING' => 'في الانتظار'
]);

// Error Messages (Arabic)
define('ERROR_MESSAGES', [
    'DB_CONNECTION_FAILED' => 'فشل في الاتصال بقاعدة البيانات',
    'INVALID_TOKEN' => 'رمز التفعيل غير صحيح',
    'INVALID_LICENSE' => 'رقم الترخيص غير صحيح',
    'EXPIRED_LICENSE' => 'انتهت صلاحية الترخيص',
    'ALREADY_ACTIVATED' => 'النظام مفعل مسبقاً',
    'ACTIVATION_FAILED' => 'فشل في تفعيل النظام',
    'INVALID_SERVER_TYPE' => 'نوع الخادم غير صحيح',
    'MISSING_REQUIRED_FIELD' => 'حقل مطلوب مفقود',
    'INVALID_DATA_FORMAT' => 'تنسيق البيانات غير صحيح',
    'SYSTEM_ERROR' => 'خطأ في النظام',
    'ACCESS_DENIED' => 'تم رفض الوصول',
    'RATE_LIMIT_EXCEEDED' => 'تم تجاوز حد الطلبات المسموح'
]);

// Success Messages (Arabic)
define('SUCCESS_MESSAGES', [
    'ACTIVATION_SUCCESS' => 'تم تفعيل النظام بنجاح',
    'UPDATE_SUCCESS' => 'تم تحديث التفعيل بنجاح',
    'VERIFICATION_SUCCESS' => 'تم التحقق من التفعيل بنجاح'
]);

// Validation Rules
define('VALIDATION_RULES', [
    'token' => [
        'required' => true,
        'min_length' => 3,
        'max_length' => 100,
        'pattern' => '/^[A-Za-z0-9_]+$/'
    ],
    'server_id' => [
        'required' => true,
        'min_length' => 8,
        'max_length' => 50
    ],
    'client_name' => [
        'required' => true,
        'min_length' => 2,
        'max_length' => 100
    ],
    'license_number' => [
        'required' => true,
        'pattern' => '/^[0-9]{18}$/'
    ]
]);

// Time Zone
date_default_timezone_set('Asia/Riyadh');

// Error Reporting
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Create logs directory if it doesn't exist
$logDir = dirname(LOG_FILE);
if (!is_dir($logDir)) {
    mkdir($logDir, 0755, true);
}

/**
 * Get configuration value
 * @param string $key
 * @param mixed $default
 * @return mixed
 */
function getConfig($key, $default = null) {
    return defined($key) ? constant($key) : $default;
}

/**
 * Get error message by key
 * @param string $key
 * @return string
 */
function getErrorMessage($key) {
    $messages = ERROR_MESSAGES;
    return isset($messages[$key]) ? $messages[$key] : 'خطأ غير معروف';
}

/**
 * Get success message by key
 * @param string $key
 * @return string
 */
function getSuccessMessage($key) {
    $messages = SUCCESS_MESSAGES;
    return isset($messages[$key]) ? $messages[$key] : 'تمت العملية بنجاح';
}

/**
 * Check if debug mode is enabled
 * @return bool
 */
function isDebugMode() {
    return APP_DEBUG === true;
}

/**
 * Generate unique request ID
 * @return string
 */
function generateRequestId() {
    return uniqid('req_', true);
}

/**
 * Sanitize input data
 * @param mixed $data
 * @return mixed
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    if (is_string($data)) {
        return trim(htmlspecialchars($data, ENT_QUOTES, 'UTF-8'));
    }
    
    return $data;
}

/**
 * Validate required environment
 */
function validateEnvironment() {
    $required_extensions = ['oci8', 'json', 'mbstring'];
    $missing = [];
    
    foreach ($required_extensions as $ext) {
        if (!extension_loaded($ext)) {
            $missing[] = $ext;
        }
    }
    
    if (!empty($missing)) {
        throw new Exception('Missing required PHP extensions: ' . implode(', ', $missing));
    }
    
    if (!is_writable(dirname(LOG_FILE))) {
        throw new Exception('Log directory is not writable: ' . dirname(LOG_FILE));
    }
}

// Validate environment on load
try {
    validateEnvironment();
} catch (Exception $e) {
    if (isDebugMode()) {
        die('Configuration Error: ' . $e->getMessage());
    } else {
        die('System configuration error. Please contact administrator.');
    }
}
?>
