// Demo version of activation.js with mock functionality
class DemoActivationSystem {
    constructor() {
        this.form = document.getElementById('activationForm');
        this.statusPanel = document.getElementById('statusPanel');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.successMessage = document.getElementById('successMessage');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.activateBtn = document.getElementById('activateBtn');
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupValidation();
        this.showWelcomeMessage();
    }

    showWelcomeMessage() {
        setTimeout(() => {
            alert('مرحباً بك في العرض التوضيحي لنظام تفعيل IAS!\n\nيمكنك استخدام الأزرار التجريبية لملء البيانات واختبار النظام.');
        }, 1000);
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.form.addEventListener('reset', () => this.handleReset());
        
        // Real-time validation
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }

    setupValidation() {
        this.validationRules = {
            token: {
                required: true,
                minLength: 3,
                pattern: /^[A-Za-z0-9_]+$/,
                message: 'رمز التفعيل يجب أن يحتوي على أحرف وأرقام فقط'
            },
            serverId1: {
                required: true,
                minLength: 8,
                maxLength: 50,
                message: 'معرف الخادم الأول مطلوب (8-50 حرف)'
            },
            serverId2: {
                required: true,
                minLength: 8,
                maxLength: 50,
                message: 'معرف الخادم الثاني مطلوب (8-50 حرف)'
            },
            clientName: {
                required: true,
                minLength: 2,
                message: 'اسم العميل مطلوب (حد أدنى حرفين)'
            },
            clientUsername: {
                required: true,
                minLength: 3,
                pattern: /^[A-Za-z0-9_]+$/,
                message: 'معرف المستخدم يجب أن يحتوي على أحرف وأرقام فقط'
            },
            sourceUsername: {
                required: true,
                minLength: 3,
                message: 'مصدر البيانات مطلوب'
            },
            licenseNumber: {
                required: true,
                pattern: /^[0-9]{18}$/,
                message: 'رقم الترخيص يجب أن يكون 18 رقم'
            },
            serverType: {
                required: true,
                message: 'نوع الخادم مطلوب'
            }
        };
    }

    validateField(field) {
        const rule = this.validationRules[field.name];
        if (!rule) return true;

        const value = field.value.trim();
        const errorElement = document.getElementById(`${field.name}-error`);

        if (rule.required && !value) {
            this.showFieldError(field, errorElement, 'هذا الحقل مطلوب');
            return false;
        }

        if (!value && !rule.required) {
            this.clearFieldError(field);
            return true;
        }

        if (rule.minLength && value.length < rule.minLength) {
            this.showFieldError(field, errorElement, `الحد الأدنى ${rule.minLength} أحرف`);
            return false;
        }

        if (rule.maxLength && value.length > rule.maxLength) {
            this.showFieldError(field, errorElement, `الحد الأقصى ${rule.maxLength} حرف`);
            return false;
        }

        if (rule.pattern && !rule.pattern.test(value)) {
            this.showFieldError(field, errorElement, rule.message);
            return false;
        }

        if (field.name === 'expiryDate' && value) {
            const selectedDate = new Date(value);
            const today = new Date();
            if (selectedDate <= today) {
                this.showFieldError(field, errorElement, 'تاريخ الانتهاء يجب أن يكون في المستقبل');
                return false;
            }
        }

        this.clearFieldError(field);
        return true;
    }

    showFieldError(field, errorElement, message) {
        field.classList.add('input-invalid');
        field.classList.remove('input-valid');
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }

    clearFieldError(field) {
        const errorElement = document.getElementById(`${field.name}-error`);
        field.classList.remove('input-invalid');
        field.classList.add('input-valid');
        errorElement.classList.remove('show');
    }

    validateForm() {
        const inputs = this.form.querySelectorAll('input[required], select[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    async handleSubmit(e) {
        e.preventDefault();

        if (!this.validateForm()) {
            this.showError('يرجى تصحيح الأخطاء في النموذج');
            return;
        }

        const formData = this.getFormData();
        
        try {
            this.showLoading();
            
            // Simulate API call delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Mock success response
            const result = {
                success: true,
                message: 'تم تفعيل النظام بنجاح! (وضع تجريبي)',
                data: {
                    activation_id: 'DEMO_' + Date.now(),
                    status: 'activated',
                    type: formData.serverType,
                    activated_date: new Date().toLocaleString('ar-SA')
                }
            };
            
            this.showSuccess(result.message);
            this.disableForm();
            
            // Show activation details
            setTimeout(() => {
                alert(`تفاصيل التفعيل:\n\nمعرف التفعيل: ${result.data.activation_id}\nالحالة: ${result.data.status}\nالنوع: ${result.data.type}\nتاريخ التفعيل: ${result.data.activated_date}`);
            }, 1000);
            
        } catch (error) {
            console.error('Demo activation error:', error);
            this.showError('حدث خطأ في العرض التوضيحي');
        }
    }

    getFormData() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value.trim();
        }

        if (data.expiryDate) {
            const date = new Date(data.expiryDate);
            data.expiryDate = date.toLocaleDateString('en-GB');
        }

        return data;
    }

    showLoading() {
        this.statusPanel.className = 'status-panel show';
        this.loadingSpinner.style.display = 'flex';
        this.successMessage.style.display = 'none';
        this.errorMessage.style.display = 'none';
        this.activateBtn.disabled = true;
    }

    showSuccess(message) {
        this.loadingSpinner.style.display = 'none';
        this.successMessage.style.display = 'flex';
        this.successMessage.querySelector('span').textContent = message;
        this.errorMessage.style.display = 'none';
        this.activateBtn.disabled = false;
    }

    showError(message) {
        this.loadingSpinner.style.display = 'none';
        this.successMessage.style.display = 'none';
        this.errorMessage.style.display = 'flex';
        this.errorText.textContent = message;
        this.activateBtn.disabled = false;
    }

    hideStatus() {
        this.statusPanel.classList.remove('show');
    }

    disableForm() {
        const inputs = this.form.querySelectorAll('input, select, button');
        inputs.forEach(input => {
            if (input.type !== 'reset') {
                input.disabled = true;
            }
        });
    }

    enableForm() {
        const inputs = this.form.querySelectorAll('input, select, button');
        inputs.forEach(input => {
            input.disabled = false;
        });
    }

    handleReset() {
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.classList.remove('input-valid', 'input-invalid');
            const errorElement = document.getElementById(`${input.name}-error`);
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        });

        this.hideStatus();
        this.enableForm();
    }

    generateHardwareId() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 12; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
}

// Demo control functions
function fillDemoData() {
    document.getElementById('token').value = 'IAS_SYS_TOKEN_2024';
    document.getElementById('serverId1').value = 'SRV_' + Math.random().toString(36).substr(2, 9).toUpperCase();
    document.getElementById('serverId2').value = 'SRV_' + Math.random().toString(36).substr(2, 9).toUpperCase();
    document.getElementById('clientName').value = 'شركة التقنية المتقدمة للحلول الرقمية';
    document.getElementById('clientUsername').value = 'admin_user';
    document.getElementById('sourceUsername').value = 'oracle_db_production';
    document.getElementById('licenseString').value = 'Enterprise License - Full Version';
    document.getElementById('licenseNumber').value = '123456789012345678';
    document.getElementById('expiryDate').value = '2025-12-31';
    document.getElementById('moduleString').value = 'IAS_MODULE_ENTERPRISE';
    document.getElementById('serverType').value = 'SERVER';
    
    // Trigger validation for all fields
    const inputs = document.querySelectorAll('input, select');
    inputs.forEach(input => {
        if (window.demoSystem) {
            window.demoSystem.validateField(input);
        }
    });
    
    alert('تم ملء البيانات التجريبية بنجاح!');
}

function simulateSuccess() {
    if (window.demoSystem) {
        window.demoSystem.showSuccess('تم تفعيل النظام بنجاح! (محاكاة)');
        setTimeout(() => {
            alert('هذه محاكاة لنجاح التفعيل.\nفي النظام الحقيقي، سيتم حفظ البيانات في قاعدة البيانات.');
        }, 500);
    }
}

function simulateError() {
    if (window.demoSystem) {
        window.demoSystem.showError('فشل في الاتصال بقاعدة البيانات (محاكاة خطأ)');
        setTimeout(() => {
            alert('هذه محاكاة لخطأ في النظام.\nفي النظام الحقيقي، سيتم تسجيل الخطأ في ملف السجلات.');
        }, 500);
    }
}

function resetForm() {
    document.getElementById('activationForm').reset();
    if (window.demoSystem) {
        window.demoSystem.handleReset();
    }
    alert('تم إعادة تعيين النموذج!');
}

// Initialize demo system
document.addEventListener('DOMContentLoaded', () => {
    window.demoSystem = new DemoActivationSystem();
    
    // Add some demo animations
    setTimeout(() => {
        const cards = document.querySelectorAll('.feature-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'all 0.6s ease';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 200);
        });
    }, 2000);
});

// Add some interactive effects
document.addEventListener('mousemove', (e) => {
    const cards = document.querySelectorAll('.feature-card');
    cards.forEach(card => {
        const rect = card.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        if (x >= 0 && x <= rect.width && y >= 0 && y <= rect.height) {
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        } else {
            card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) translateZ(0px)';
        }
    });
});
