class ActivationSystem {
    constructor() {
        this.form = document.getElementById('activationForm');
        this.statusPanel = document.getElementById('statusPanel');
        this.loadingSpinner = document.getElementById('loadingSpinner');
        this.successMessage = document.getElementById('successMessage');
        this.errorMessage = document.getElementById('errorMessage');
        this.errorText = document.getElementById('errorText');
        this.activateBtn = document.getElementById('activateBtn');
        
        this.init();
    }

    init() {
        this.bindEvents();
        this.setupValidation();
    }

    bindEvents() {
        this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        this.form.addEventListener('reset', () => this.handleReset());
        
        // Real-time validation
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.clearFieldError(input));
        });
    }

    setupValidation() {
        // Setup validation rules
        this.validationRules = {
            token: {
                required: true,
                minLength: 3,
                pattern: /^[A-Za-z0-9_]+$/,
                message: 'رمز التفعيل يجب أن يحتوي على أحرف وأرقام فقط'
            },
            serverId1: {
                required: true,
                minLength: 8,
                maxLength: 50,
                message: 'معرف الخادم الأول مطلوب (8-50 حرف)'
            },
            serverId2: {
                required: true,
                minLength: 8,
                maxLength: 50,
                message: 'معرف الخادم الثاني مطلوب (8-50 حرف)'
            },
            clientName: {
                required: true,
                minLength: 2,
                message: 'اسم العميل مطلوب (حد أدنى حرفين)'
            },
            clientUsername: {
                required: true,
                minLength: 3,
                pattern: /^[A-Za-z0-9_]+$/,
                message: 'معرف المستخدم يجب أن يحتوي على أحرف وأرقام فقط'
            },
            sourceUsername: {
                required: true,
                minLength: 3,
                message: 'مصدر البيانات مطلوب'
            },
            licenseNumber: {
                required: true,
                pattern: /^[0-9]{18}$/,
                message: 'رقم الترخيص يجب أن يكون 18 رقم'
            },
            serverType: {
                required: true,
                message: 'نوع الخادم مطلوب'
            }
        };
    }

    validateField(field) {
        const rule = this.validationRules[field.name];
        if (!rule) return true;

        const value = field.value.trim();
        const errorElement = document.getElementById(`${field.name}-error`);

        // Required validation
        if (rule.required && !value) {
            this.showFieldError(field, errorElement, 'هذا الحقل مطلوب');
            return false;
        }

        // Skip other validations if field is empty and not required
        if (!value && !rule.required) {
            this.clearFieldError(field);
            return true;
        }

        // Length validation
        if (rule.minLength && value.length < rule.minLength) {
            this.showFieldError(field, errorElement, `الحد الأدنى ${rule.minLength} أحرف`);
            return false;
        }

        if (rule.maxLength && value.length > rule.maxLength) {
            this.showFieldError(field, errorElement, `الحد الأقصى ${rule.maxLength} حرف`);
            return false;
        }

        // Pattern validation
        if (rule.pattern && !rule.pattern.test(value)) {
            this.showFieldError(field, errorElement, rule.message);
            return false;
        }

        // Custom validations
        if (field.name === 'expiryDate' && value) {
            const selectedDate = new Date(value);
            const today = new Date();
            if (selectedDate <= today) {
                this.showFieldError(field, errorElement, 'تاريخ الانتهاء يجب أن يكون في المستقبل');
                return false;
            }
        }

        this.clearFieldError(field);
        return true;
    }

    showFieldError(field, errorElement, message) {
        field.classList.add('input-invalid');
        field.classList.remove('input-valid');
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }

    clearFieldError(field) {
        const errorElement = document.getElementById(`${field.name}-error`);
        field.classList.remove('input-invalid');
        field.classList.add('input-valid');
        errorElement.classList.remove('show');
    }

    validateForm() {
        const inputs = this.form.querySelectorAll('input[required], select[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    async handleSubmit(e) {
        e.preventDefault();

        if (!this.validateForm()) {
            this.showError('يرجى تصحيح الأخطاء في النموذج');
            return;
        }

        const formData = this.getFormData();
        
        try {
            this.showLoading();
            const result = await this.submitActivation(formData);
            
            if (result.success) {
                this.showSuccess('تم تفعيل النظام بنجاح!');
                this.disableForm();
            } else {
                this.showError(result.message || 'فشل في تفعيل النظام');
            }
        } catch (error) {
            console.error('Activation error:', error);
            this.showError('حدث خطأ في الاتصال بالخادم');
        }
    }

    getFormData() {
        const formData = new FormData(this.form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value.trim();
        }

        // Format date if provided
        if (data.expiryDate) {
            const date = new Date(data.expiryDate);
            data.expiryDate = date.toLocaleDateString('en-GB'); // DD/MM/YYYY format
        }

        return data;
    }

    async submitActivation(data) {
        // Simulate API call - replace with actual endpoint
        const response = await fetch('api/activate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    showLoading() {
        this.statusPanel.className = 'status-panel show';
        this.loadingSpinner.style.display = 'flex';
        this.successMessage.style.display = 'none';
        this.errorMessage.style.display = 'none';
        this.activateBtn.disabled = true;
    }

    showSuccess(message) {
        this.loadingSpinner.style.display = 'none';
        this.successMessage.style.display = 'flex';
        this.successMessage.querySelector('span').textContent = message;
        this.errorMessage.style.display = 'none';
        this.activateBtn.disabled = false;
    }

    showError(message) {
        this.loadingSpinner.style.display = 'none';
        this.successMessage.style.display = 'none';
        this.errorMessage.style.display = 'flex';
        this.errorText.textContent = message;
        this.activateBtn.disabled = false;
    }

    hideStatus() {
        this.statusPanel.classList.remove('show');
    }

    disableForm() {
        const inputs = this.form.querySelectorAll('input, select, button');
        inputs.forEach(input => {
            if (input.type !== 'reset') {
                input.disabled = true;
            }
        });
    }

    enableForm() {
        const inputs = this.form.querySelectorAll('input, select, button');
        inputs.forEach(input => {
            input.disabled = false;
        });
    }

    handleReset() {
        // Clear all validation states
        const inputs = this.form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.classList.remove('input-valid', 'input-invalid');
            const errorElement = document.getElementById(`${input.name}-error`);
            if (errorElement) {
                errorElement.classList.remove('show');
            }
        });

        this.hideStatus();
        this.enableForm();
    }

    // Utility method to generate hardware ID (simulation)
    generateHardwareId() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        ctx.textBaseline = 'top';
        ctx.font = '14px Arial';
        ctx.fillText('Hardware fingerprint', 2, 2);
        
        const fingerprint = canvas.toDataURL();
        return btoa(fingerprint).substring(0, 12);
    }

    // Method to auto-fill some fields for testing
    autoFillDemo() {
        document.getElementById('token').value = 'IAS_SYS_TOKEN_2024';
        document.getElementById('serverId1').value = this.generateHardwareId();
        document.getElementById('serverId2').value = this.generateHardwareId() + '_2';
        document.getElementById('clientName').value = 'شركة التقنية المتقدمة';
        document.getElementById('clientUsername').value = 'admin_user';
        document.getElementById('sourceUsername').value = 'oracle_db_source';
        document.getElementById('licenseNumber').value = '123456789012345678';
        document.getElementById('moduleString').value = 'IAS_MODULE_MAIN';
    }
}

// Initialize the activation system when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.activationSystem = new ActivationSystem();
    
    // Add demo button for testing (remove in production)
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
        const demoBtn = document.createElement('button');
        demoBtn.textContent = 'ملء تجريبي';
        demoBtn.type = 'button';
        demoBtn.className = 'btn-secondary';
        demoBtn.style.position = 'fixed';
        demoBtn.style.top = '20px';
        demoBtn.style.left = '20px';
        demoBtn.style.zIndex = '1000';
        demoBtn.onclick = () => window.activationSystem.autoFillDemo();
        document.body.appendChild(demoBtn);
    }
});
