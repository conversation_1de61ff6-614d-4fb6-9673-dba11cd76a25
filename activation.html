<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شاشة تفعيل النظام - IAS System</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <div class="activation-card">
            <div class="header">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>IAS System</h1>
                </div>
                <h2>تفعيل النظام</h2>
                <p>يرجى إدخال بيانات التفعيل للمتابعة</p>
            </div>

            <form id="activationForm" class="activation-form">
                <div class="form-group">
                    <label for="token">
                        <i class="fas fa-key"></i>
                        رمز التفعيل (Token)
                    </label>
                    <input type="text" id="token" name="token" required 
                           placeholder="أدخل رمز التفعيل" maxlength="100">
                    <span class="error-message" id="token-error"></span>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="serverId1">
                            <i class="fas fa-server"></i>
                            معرف الخادم الأول
                        </label>
                        <input type="text" id="serverId1" name="serverId1" required 
                               placeholder="معرف الخادم 1" maxlength="50">
                        <span class="error-message" id="serverId1-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="serverId2">
                            <i class="fas fa-server"></i>
                            معرف الخادم الثاني
                        </label>
                        <input type="text" id="serverId2" name="serverId2" required 
                               placeholder="معرف الخادم 2" maxlength="50">
                        <span class="error-message" id="serverId2-error"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="clientName">
                            <i class="fas fa-user"></i>
                            اسم العميل
                        </label>
                        <input type="text" id="clientName" name="clientName" required 
                               placeholder="اسم العميل" maxlength="100">
                        <span class="error-message" id="clientName-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="clientUsername">
                            <i class="fas fa-user-tag"></i>
                            معرف المستخدم
                        </label>
                        <input type="text" id="clientUsername" name="clientUsername" required 
                               placeholder="معرف المستخدم" maxlength="50">
                        <span class="error-message" id="clientUsername-error"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="sourceUsername">
                        <i class="fas fa-database"></i>
                        مصدر البيانات
                    </label>
                    <input type="text" id="sourceUsername" name="sourceUsername" required 
                           placeholder="مصدر البيانات" maxlength="100">
                    <span class="error-message" id="sourceUsername-error"></span>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="licenseString">
                            <i class="fas fa-certificate"></i>
                            نص الترخيص
                        </label>
                        <input type="text" id="licenseString" name="licenseString" 
                               placeholder="نص الترخيص (اختياري)" maxlength="200">
                        <span class="error-message" id="licenseString-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="licenseNumber">
                            <i class="fas fa-hashtag"></i>
                            رقم الترخيص
                        </label>
                        <input type="text" id="licenseNumber" name="licenseNumber" required 
                               placeholder="رقم الترخيص" maxlength="20">
                        <span class="error-message" id="licenseNumber-error"></span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="expiryDate">
                            <i class="fas fa-calendar-alt"></i>
                            تاريخ الانتهاء
                        </label>
                        <input type="date" id="expiryDate" name="expiryDate">
                        <span class="error-message" id="expiryDate-error"></span>
                    </div>

                    <div class="form-group">
                        <label for="moduleString">
                            <i class="fas fa-puzzle-piece"></i>
                            نص الوحدة
                        </label>
                        <input type="text" id="moduleString" name="moduleString" 
                               placeholder="نص الوحدة" maxlength="100">
                        <span class="error-message" id="moduleString-error"></span>
                    </div>
                </div>

                <div class="form-group">
                    <label for="serverType">
                        <i class="fas fa-cogs"></i>
                        نوع الخادم
                    </label>
                    <select id="serverType" name="serverType" required>
                        <option value="SERVER">خادم (SERVER)</option>
                        <option value="CLIENT">عميل (CLIENT)</option>
                        <option value="ADMIN">مدير (ADMIN)</option>
                    </select>
                    <span class="error-message" id="serverType-error"></span>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary" id="activateBtn">
                        <i class="fas fa-rocket"></i>
                        تفعيل النظام
                    </button>
                    <button type="reset" class="btn-secondary">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين
                    </button>
                </div>
            </form>

            <div class="status-panel" id="statusPanel">
                <div class="status-content">
                    <div class="loading" id="loadingSpinner">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>جاري التفعيل...</span>
                    </div>
                    <div class="success-message" id="successMessage">
                        <i class="fas fa-check-circle"></i>
                        <span>تم تفعيل النظام بنجاح!</span>
                    </div>
                    <div class="error-message" id="errorMessage">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="errorText">حدث خطأ أثناء التفعيل</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="info-panel">
            <h3><i class="fas fa-info-circle"></i> معلومات مهمة</h3>
            <ul>
                <li>تأكد من صحة جميع البيانات المدخلة</li>
                <li>رمز التفعيل حساس لحالة الأحرف</li>
                <li>يجب أن يكون الخادم متصلاً بقاعدة البيانات</li>
                <li>في حالة وجود مشاكل، تواصل مع الدعم الفني</li>
            </ul>
        </div>
    </div>

    <script src="activation.js"></script>
</body>
</html>
