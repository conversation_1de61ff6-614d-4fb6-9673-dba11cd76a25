<?php
/**
 * Oracle Database Connection Class
 * فئة الاتصال بقاعدة بيانات Oracle
 */

class OracleDatabase {
    private $connection;
    private $host;
    private $port;
    private $serviceName;
    private $username;
    private $password;
    private $charset;
    private $isConnected = false;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->port = DB_PORT;
        $this->serviceName = DB_SERVICE_NAME;
        $this->username = DB_USERNAME;
        $this->password = DB_PASSWORD;
        $this->charset = DB_CHARSET;
    }
    
    /**
     * Connect to Oracle database
     * @return bool
     */
    public function connect() {
        try {
            $tns = "(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST={$this->host})(PORT={$this->port}))(CONNECT_DATA=(SERVICE_NAME={$this->serviceName})))";
            
            $this->connection = oci_connect(
                $this->username,
                $this->password,
                $tns,
                $this->charset
            );
            
            if (!$this->connection) {
                $error = oci_error();
                throw new Exception('Oracle connection failed: ' . $error['message']);
            }
            
            $this->isConnected = true;
            return true;
            
        } catch (Exception $e) {
            error_log('Database connection error: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Disconnect from database
     */
    public function disconnect() {
        if ($this->connection && $this->isConnected) {
            oci_close($this->connection);
            $this->isConnected = false;
        }
    }
    
    /**
     * Check if connected
     * @return bool
     */
    public function isConnected() {
        return $this->isConnected && $this->connection;
    }
    
    /**
     * Prepare SQL statement
     * @param string $sql
     * @return resource|false
     */
    public function prepare($sql) {
        if (!$this->isConnected()) {
            throw new Exception('Database not connected');
        }
        
        $statement = oci_parse($this->connection, $sql);
        if (!$statement) {
            $error = oci_error($this->connection);
            throw new Exception('SQL prepare failed: ' . $error['message']);
        }
        
        return new OracleStatement($statement, $this->connection);
    }
    
    /**
     * Execute SQL query
     * @param string $sql
     * @param array $params
     * @return resource|false
     */
    public function query($sql, $params = []) {
        $stmt = $this->prepare($sql);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        return $stmt->execute() ? $stmt : false;
    }
    
    /**
     * Get new cursor for stored procedures
     * @return resource
     */
    public function getNewCursor() {
        if (!$this->isConnected()) {
            throw new Exception('Database not connected');
        }
        
        return oci_new_cursor($this->connection);
    }
    
    /**
     * Begin transaction
     */
    public function beginTransaction() {
        // Oracle auto-commits by default, so we disable it
        oci_set_autocommit($this->connection, false);
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        if ($this->isConnected()) {
            oci_commit($this->connection);
            oci_set_autocommit($this->connection, true);
        }
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        if ($this->isConnected()) {
            oci_rollback($this->connection);
            oci_set_autocommit($this->connection, true);
        }
    }
    
    /**
     * Get last error
     * @return array|null
     */
    public function getLastError() {
        return $this->connection ? oci_error($this->connection) : null;
    }
    
    /**
     * Escape string for SQL
     * @param string $string
     * @return string
     */
    public function escape($string) {
        return str_replace("'", "''", $string);
    }
    
    /**
     * Get database version
     * @return string|null
     */
    public function getVersion() {
        if (!$this->isConnected()) {
            return null;
        }
        
        $sql = "SELECT banner FROM v\$version WHERE banner LIKE 'Oracle%'";
        $stmt = $this->prepare($sql);
        
        if ($stmt->execute()) {
            $result = $stmt->fetch();
            return $result ? $result['BANNER'] : null;
        }
        
        return null;
    }
}

/**
 * Oracle Statement Wrapper Class
 */
class OracleStatement {
    private $statement;
    private $connection;
    private $boundParams = [];
    
    public function __construct($statement, $connection) {
        $this->statement = $statement;
        $this->connection = $connection;
    }
    
    /**
     * Bind parameter to statement
     * @param string $param
     * @param mixed $value
     * @param int $type
     * @return bool
     */
    public function bindParam($param, &$value, $type = SQLT_CHR) {
        $this->boundParams[$param] = &$value;
        return oci_bind_by_name($this->statement, $param, $value, -1, $type);
    }
    
    /**
     * Execute statement
     * @return bool
     */
    public function execute() {
        $result = oci_execute($this->statement, OCI_DEFAULT);
        
        if (!$result) {
            $error = oci_error($this->statement);
            throw new Exception('Statement execution failed: ' . $error['message']);
        }
        
        return $result;
    }
    
    /**
     * Fetch single row
     * @param int $mode
     * @return array|false
     */
    public function fetch($mode = OCI_ASSOC) {
        return oci_fetch_array($this->statement, $mode);
    }
    
    /**
     * Fetch all rows
     * @param int $mode
     * @return array
     */
    public function fetchAll($mode = OCI_ASSOC) {
        $results = [];
        while ($row = $this->fetch($mode)) {
            $results[] = $row;
        }
        return $results;
    }
    
    /**
     * Get number of rows affected
     * @return int
     */
    public function rowCount() {
        return oci_num_rows($this->statement);
    }
    
    /**
     * Get error info
     * @return array|null
     */
    public function errorInfo() {
        return oci_error($this->statement);
    }
    
    /**
     * Free statement resources
     */
    public function free() {
        if ($this->statement) {
            oci_free_statement($this->statement);
        }
    }
    
    /**
     * Destructor
     */
    public function __destruct() {
        $this->free();
    }
}

/**
 * Database Logger Class
 */
class Logger {
    private $logFile;
    private $logLevel;

    public function __construct() {
        $this->logFile = LOG_FILE;
        $this->logLevel = LOG_LEVEL;
    }

    /**
     * Log info message
     * @param string $message
     * @param array $context
     */
    public function info($message, $context = []) {
        $this->log('INFO', $message, $context);
    }

    /**
     * Log error message
     * @param string $message
     * @param array $context
     */
    public function error($message, $context = []) {
        $this->log('ERROR', $message, $context);
    }

    /**
     * Log warning message
     * @param string $message
     * @param array $context
     */
    public function warning($message, $context = []) {
        $this->log('WARNING', $message, $context);
    }

    /**
     * Log debug message
     * @param string $message
     * @param array $context
     */
    public function debug($message, $context = []) {
        if (APP_DEBUG) {
            $this->log('DEBUG', $message, $context);
        }
    }

    /**
     * Write log entry
     * @param string $level
     * @param string $message
     * @param array $context
     */
    private function log($level, $message, $context = []) {
        if (!LOG_ENABLED) {
            return;
        }

        $timestamp = date('Y-m-d H:i:s');
        $contextStr = !empty($context) ? ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE) : '';
        $logEntry = "[{$timestamp}] [{$level}] {$message}{$contextStr}" . PHP_EOL;

        // Rotate log if too large
        if (file_exists($this->logFile) && filesize($this->logFile) > LOG_MAX_SIZE) {
            $this->rotateLog();
        }

        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    /**
     * Rotate log file
     */
    private function rotateLog() {
        $backupFile = $this->logFile . '.' . date('Y-m-d-H-i-s') . '.bak';
        rename($this->logFile, $backupFile);

        // Keep only last 5 backup files
        $logDir = dirname($this->logFile);
        $backupFiles = glob($logDir . '/*.bak');
        if (count($backupFiles) > 5) {
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });

            for ($i = 0; $i < count($backupFiles) - 5; $i++) {
                unlink($backupFiles[$i]);
            }
        }
    }
}
?>
