CREATE OR <PERSON><PERSON>LACE PACKAGE IAS_SYS.init_pkg_new
AS

 
 

   TYPE REC_TABLES IS RECORD (
      OBJ_ID              IAS_OBJ_VIEWS.OBJ_ID%TYPE,
      OBJ_TYPE            IAS_OBJ_VIEWS.OBJ_TYPE%TYPE,
      OBJ_ACCESS_ID1      IAS_OBJ_VIEWS.OBJ_ACCESS_ID1%TYPE,
      OBJ_ACCESS_ID2      IAS_OBJ_VIEWS.OBJ_ACCESS_ID2%TYPE,
      OBJ_NAME            IAS_OBJ_VIEWS.OBJ_NAME%TYPE,
      OBJ_DEVELOPER_KEY   IAS_OBJ_VIEWS.OBJ_DEVELOPER_KEY%TYPE,
      OBJ_ACCESS_KEY      IAS_OBJ_VIEWS.OBJ_ACCESS_KEY%TYPE,
      OBJ_SA_ID           IAS_OBJ_VIEWS.OBJ_SA_ID%TYPE,
      OBJ_PA_ID           IAS_OBJ_VIEWS.OBJ_PA_ID%TYPE,
      OBJ_FA_ID           IAS_OBJ_VIEWS.OBJ_FA_ID%TYPE,
      OBJ_IA_ID           IAS_OBJ_VIEWS.OBJ_IA_ID%TYPE,
      OBJ_UPDATED_BY      IAS_OBJ_VIEWS.OBJ_UPDATED_BY%TYPE,
      OBJ_UPDATED_DATE    IAS_OBJ_VIEWS.OBJ_UPDATED_DATE%TYPE
   );

   PROCEDURE INIT_KEY;

   PROCEDURE SET_MODULE (P_MODULE VARCHAR2);

   PROCEDURE INIT_CONTEXT (P_CONTEXT VARCHAR2, P_PRED VARCHAR2, P_VAL VARCHAR2);

   PROCEDURE CLEAR_CONTEXT (P_CONTEXT VARCHAR2, P_PRED VARCHAR2);

   FUNCTION GET_MY_CREDIENTALS (
      P_A1   IN   VARCHAR2,
      P_A2   IN   VARCHAR2,
      P_T    IN   VARCHAR2,
      P_U    IN   VARCHAR2
   )
      RETURN REC_TABLES;

   FUNCTION GET_DET_CREDIENTALS (P_SER IN VARCHAR2, P_MOD IN VARCHAR2)
      RETURN VARCHAR2;

   PROCEDURE INIT_CONTEXT2 (
      P_CONTEXT   VARCHAR2,
      P_PRED      VARCHAR2,
      P_VAL       VARCHAR2,
      P_CLIENT    VARCHAR2
   );


   PROCEDURE SUBSCRIBE (
      P_S_ID1   VARCHAR2,
      P_S_ID2   VARCHAR2,
      P_S_ID3   VARCHAR2,
      P_S_ID4   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   );

   PROCEDURE INIT_DETAILS (
      P_INIT_TYPE   VARCHAR2,
      P_SER         VARCHAR2,
      P_MOD         VARCHAR2,
      P_TOKEN       VARCHAR2,
      P_S_ID1       VARCHAR2,
      P_S_ID2       VARCHAR2,
      P_C_TN        VARCHAR2,
      P_C_UN        VARCHAR2,
      P_SOURCE_UN   VARCHAR2,
      P_L_STRING    VARCHAR2,
      P_L_UN        VARCHAR2,
      P_L_UN2       VARCHAR2,
      P_L_UN3       VARCHAR2
   );

   PROCEDURE INIT_MAIN (
      P_TOKEN       VARCHAR2,
      P_S_ID1       VARCHAR2,
      P_S_ID2       VARCHAR2,
      P_C_TN        VARCHAR2,
      P_C_UN        VARCHAR2,
      P_SOURCE_UN   VARCHAR2,
      P_L_STRING    VARCHAR2,
      P_L_UN        VARCHAR2,
      P_L_ED        VARCHAR2,
      P_REACT       VARCHAR2,
      P_MOD_STR     VARCHAR2,
      P_ST          VARCHAR2 DEFAULT 'SERVER'
   );

   PROCEDURE INIT_RELEAM (
      P_TOKEN   VARCHAR2,
      P_S_ID1   VARCHAR2,
      P_S_ID2   VARCHAR2,
      P_S_ID3   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   );

   PROCEDURE INIT_MAIN2 (
      P_C_ID1   VARCHAR2,
      P_C_ID2   VARCHAR2,
      P_C_ID3   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   );

   PROCEDURE INIT_MAIN3 (P_C_ID1 NUMBER, P_C_ID2 NUMBER);

   PROCEDURE INIT_MAIN4 (P_C_ID1 NUMBER, P_C_ID2 NUMBER);

   PROCEDURE INIT_MAIN5 (P_C_ID1 NUMBER, P_C_ID2 NUMBER);

   PROCEDURE PRE_QUERY (P_QUERY_SEARCH IN REC_TABLES);

   TYPE REC_TABLES_REF IS REF CURSOR
      RETURN REC_TABLES;

   PROCEDURE QUERY_DET (RESULTSET IN OUT REC_TABLES_REF);

   PROCEDURE QUERY (RESULTSET IN OUT REC_TABLES_REF);

   PROCEDURE QUERY2 (RESULTSET IN OUT REC_TABLES_REF);

   PROCEDURE QUERY3 (
      P_A1        IN       VARCHAR2,
      P_A2        IN       VARCHAR2,
      P_T         IN       VARCHAR2,
      P_U         IN       VARCHAR2,
      RESULTSET   IN OUT   REC_TABLES_REF
   );

   PROCEDURE UPDATE_REC (
      P_ID   IN   VARCHAR2,
      P_T    IN   VARCHAR2,
      P_U    IN   VARCHAR2,
      P_SA        VARCHAR2,
      P_FA        VARCHAR2,
      P_PA        VARCHAR2,
      P_IA        VARCHAR2
   );

   PROCEDURE REGISTER_ACCESS (P_ID IN VARCHAR2, P_T IN VARCHAR2);

   PROCEDURE UPDATE_SOURCE (P_ID IN VARCHAR2, P_S IN VARCHAR2);

   FUNCTION ENCRYPTSTRING (P_DATA IN VARCHAR2, P_KEY IN VARCHAR2 DEFAULT NULL)
      RETURN VARCHAR2;

   FUNCTION DECRYPTSTRING (P_DATA IN VARCHAR2, P_KEY IN VARCHAR2 DEFAULT NULL)
      RETURN VARCHAR2;

   FUNCTION GVAL (P_ID VARCHAR2, P_KEY VARCHAR2 DEFAULT NULL)
      RETURN VARCHAR2;

   FUNCTION GETKEY
      RETURN VARCHAR2;

   FUNCTION GETVERSION
      RETURN VARCHAR2;
END INIT_PKG_new;
/


CREATE OR REPLACE PACKAGE BODY IAS_SYS.init_pkg_new
IS
   G_SETUP_FOR_ALL        BOOLEAN         := FALSE;
   G_LOOP_BACK            BOOLEAN         := FALSE;
   G_MODULE               VARCHAR2 (100)  := NULL;
   G_LOOP_ID              VARCHAR2 (1000);
   G_ALLOW_PC_ADMIN       NUMBER          := 0;
   G_OPERATION_MODE       NUMBER          := 1;
   G_MAX_USER             NUMBER          := 0;
   G_LICENSE_TEXT         VARCHAR2 (1000);
   G_TOKEN                VARCHAR2 (1000) := 'IAS_SYS';
   G_MKEY                 VARCHAR2 (100);
   G_ACCESS_TIMES         NUMBER (20);
   G_ACCESS_DATE          VARCHAR2 (1000);
   G_DATA_SRC             VARCHAR2 (1000);
   G_LIC_TEXT             VARCHAR2 (1000);
   G_T                    VARCHAR2 (1000);
   G_U                    VARCHAR2 (1000);
   G_S_ID                 VARCHAR2 (1000);
   G_S_ID2                VARCHAR2 (1000);
   G_S_ID3                VARCHAR2 (1000);
   G_S_ID4                VARCHAR2 (1000);
   G_CHARKEY              VARCHAR2 (48);
   G_STRINGFUNCTION       VARCHAR2 (1);
   G_RAWFUNCTION          VARCHAR2 (1);
   G_STRINGWHICH          VARCHAR2 (75);
   G_RAWWHICH             VARCHAR2 (75);
   G_CHUNKSIZE   CONSTANT NUMBER          DEFAULT 32000;
   QUERY_SEARCH           REC_TABLES;

   TYPE QUERY_SEARCH_REF IS REF CURSOR
      RETURN REC_TABLES;

   PROCEDURE SETKEY (P_KEY IN VARCHAR2)
   AS
   BEGIN
      G_CHARKEY := P_KEY;
      G_CHARKEY := P_KEY;

      IF (LENGTH (G_CHARKEY) NOT IN (8, 16, 24, 16, 32, 48))
      THEN
         RETURN;
      END IF;

      SELECT DECODE (LENGTH (G_CHARKEY), 8, '', '3'),
             DECODE (LENGTH (G_CHARKEY),
                     8, '',
                     16, '',
                     24, ', which=>dbms_obfuscation_toolkit.ThreeKeyMode'
                    ),
             DECODE (LENGTH (G_CHARKEY), 16, '', '3'),
             DECODE (LENGTH (G_CHARKEY),
                     16, '',
                     32, '',
                     48, ', which=>dbms_obfuscation_toolkit.ThreeKeyMode'
                    )
        INTO G_STRINGFUNCTION,
             G_STRINGWHICH,
             G_RAWFUNCTION,
             G_RAWWHICH
        FROM DUAL;
   END;


   FUNCTION PADSTR (P_STR IN VARCHAR2)
      RETURN VARCHAR2
   AS
      L_LEN   NUMBER DEFAULT LENGTH (P_STR);
   BEGIN
      RETURN    TO_CHAR (L_LEN, 'fm00000009')
             || RPAD (P_STR,
                      (TRUNC (L_LEN / 8) + SIGN (MOD (L_LEN, 8))) * 8,
                      CHR (0)
                     );
   END;

   FUNCTION PADRAW (P_RAW IN RAW)
      RETURN RAW
   AS
      L_LEN   NUMBER DEFAULT UTL_RAW.LENGTH (P_RAW);
   BEGIN
      RETURN UTL_RAW.CONCAT (UTL_RAW.CAST_TO_RAW (TO_CHAR (L_LEN,
                                                           'fm00000009')
                                                 ),
                             P_RAW,
                             UTL_RAW.CAST_TO_RAW (RPAD (CHR (0),
                                                          (8 - MOD (L_LEN, 8)
                                                          )
                                                        * SIGN (MOD (L_LEN, 8)),
                                                        CHR (0)
                                                       )
                                                 )
                            );
   END;

   FUNCTION ENCRYPTSTRING (P_DATA IN VARCHAR2, P_KEY IN VARCHAR2 DEFAULT NULL)
      RETURN VARCHAR2
   AS
      L_ENCRYPTED   LONG;
   BEGIN
      SETKEY (P_KEY);

      EXECUTE IMMEDIATE    'begin
dbms_obfuscation_toolkit.des'
                        || G_STRINGFUNCTION
                        || 'encrypt
( input_string => :1, key_string => :2, encrypted_string => :3'
                        || G_STRINGWHICH
                        || ' );
end;'
                  USING IN PADSTR (P_DATA), IN G_CHARKEY, IN OUT L_ENCRYPTED;

      RETURN L_ENCRYPTED;
   EXCEPTION
      WHEN OTHERS
      THEN
         RETURN NULL;
   END;


   FUNCTION UNPADSTR (P_STR IN VARCHAR2)
      RETURN VARCHAR2
   IS
   BEGIN
      RETURN SUBSTR (P_STR, 9, TO_NUMBER (SUBSTR (P_STR, 1, 8)));
   END;


   FUNCTION DECRYPTSTRING (P_DATA IN VARCHAR2, P_KEY IN VARCHAR2 DEFAULT NULL)
      RETURN VARCHAR2
   AS
      L_STRING   LONG;
   BEGIN
      SETKEY (P_KEY);

      EXECUTE IMMEDIATE    'begin
dbms_obfuscation_toolkit.des'
                        || G_STRINGFUNCTION
                        || 'decrypt
( input_string => :1, key_string => :2, decrypted_string => :3'
                        || G_STRINGWHICH
                        || ' );
end;'
                  USING IN P_DATA, IN G_CHARKEY, IN OUT L_STRING;

      RETURN UNPADSTR (L_STRING);
   EXCEPTION
      WHEN OTHERS
      THEN
         RETURN NULL;
   END;


   FUNCTION ENCRYPTRAW (P_DATA IN RAW, P_KEY IN RAW DEFAULT NULL)
      RETURN RAW
   AS
      L_ENCRYPTED   LONG RAW;
   BEGIN
      SETKEY (P_KEY);

      EXECUTE IMMEDIATE    'begin
dbms_obfuscation_toolkit.des'
                        || G_RAWFUNCTION
                        || 'encrypt
( input => :1, key => :2, encrypted_data => :3'
                        || G_RAWWHICH
                        || ' );
end;'
                  USING IN     PADRAW (P_DATA),
                        IN     HEXTORAW (G_CHARKEY),
                        IN OUT L_ENCRYPTED;

      RETURN L_ENCRYPTED;
   END;


   FUNCTION UNPADRAW (P_RAW IN RAW)
      RETURN RAW
   IS
   BEGIN
      RETURN UTL_RAW.SUBSTR
                 (P_RAW,
                  9,
                  TO_NUMBER (UTL_RAW.CAST_TO_VARCHAR2 (UTL_RAW.SUBSTR (P_RAW,
                                                                       1,
                                                                       8
                                                                      )
                                                      )
                            )
                 );
   END;

   FUNCTION DECRYPTRAW (P_DATA IN RAW, P_KEY IN RAW DEFAULT NULL)
      RETURN RAW
   AS
      L_STRING   LONG RAW;
   BEGIN
      SETKEY (P_KEY);

      EXECUTE IMMEDIATE    'begin
dbms_obfuscation_toolkit.des'
                        || G_RAWFUNCTION
                        || 'decrypt
( input => :1, key => :2, decrypted_data => :3 '
                        || G_RAWWHICH
                        || ' );
end;'
                  USING IN P_DATA, IN HEXTORAW (G_CHARKEY), IN OUT L_STRING;

      RETURN UNPADRAW (L_STRING);
   END;

   PROCEDURE PRE_QUERY (P_QUERY_SEARCH IN REC_TABLES)
   IS
   BEGIN
      NULL;
   END;

   PROCEDURE INIT_KEY
   IS
   BEGIN
      G_MKEY := SYS_CONTEXT ('ys_context', 'IAS_SYS');
      RETURN;
   END;

   PROCEDURE QUERY (RESULTSET IN OUT REC_TABLES_REF)
   IS
   BEGIN
      INIT_KEY;

      OPEN RESULTSET FOR
         SELECT   MOD_ID OBJ_ID, OBJ_TYPE, OBJ_ACCESS_ID1, OBJ_ACCESS_ID2,
                  DECODE (OBJ_NAME, 'Empty', NULL, OBJ_NAME) OBJ_NAME,
                  DECODE (OBJ_DEVELOPER_KEY,
                          'Empty', NULL,
                          OBJ_DEVELOPER_KEY
                         ) OBJ_DEVELOPER_KEY,
                  OBJ_ACCESS_KEY, OBJ_SA_ID, OBJ_PA_ID, OBJ_FA_ID, OBJ_IA_ID,
                  OBJ_UPDATED_BY, OBJ_UPDATED_DATE
             FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID, MOD_ID,
                          DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                          DECRYPTSTRING (OBJ_ACCESS_ID1,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID1,
                          DECRYPTSTRING (OBJ_ACCESS_ID2,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID2,
                          DECRYPTSTRING (OBJ_BLOB_ID4, G_MKEY) OBJ_NAME,
                          DECRYPTSTRING (OBJ_BLOB_ID5,
                                         G_MKEY
                                        ) OBJ_DEVELOPER_KEY,
                          DECRYPTSTRING (OBJ_ACCESS_KEY,
                                         G_MKEY
                                        ) OBJ_ACCESS_KEY,
                          DECRYPTSTRING (OBJ_ELEMENT_STATE, G_MKEY) OBJ_SA_ID,
                          DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                          DECRYPTSTRING (OBJ_BLOB_ID2, G_MKEY) OBJ_FA_ID,
                          DECRYPTSTRING (OBJ_BLOB_ID3, G_MKEY) OBJ_IA_ID,
                          DECRYPTSTRING (OBJ_BLOB_ID6, G_MKEY) OBJ_UPDATED_BY,
                          DECRYPTSTRING (OBJ_CREATED_DATE,
                                         G_MKEY
                                        ) OBJ_UPDATED_DATE
                     FROM IAS_OBJ_VIEWS)
            WHERE OBJ_TYPE IN ('SERVER', 'ADMIN', 'CLIENT', 'PENDING')
              AND OBJ_ID IS NOT NULL
         ORDER BY OBJ_NAME, OBJ_ID;
   END;

   PROCEDURE QUERY2 (RESULTSET IN OUT REC_TABLES_REF)
   IS
   BEGIN
      INIT_KEY;

      OPEN RESULTSET FOR
         SELECT   OBJ_ID, OBJ_TYPE, OBJ_ACCESS_ID1, OBJ_ACCESS_ID2, OBJ_NAME,
                  OBJ_DEVELOPER_KEY, OBJ_ACCESS_KEY, OBJ_SA_ID, OBJ_PA_ID,
                  OBJ_FA_ID, OBJ_IA_ID, OBJ_UPDATED_BY, OBJ_UPDATED_DATE
             FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID,
                          DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                          DECRYPTSTRING (OBJ_ACCESS_ID1,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID1,
                          DECRYPTSTRING (OBJ_ACCESS_ID2,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID2,
                          DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
                          DECRYPTSTRING (OBJ_DEVELOPER_KEY,
                                         G_MKEY
                                        ) OBJ_DEVELOPER_KEY,
                          DECRYPTSTRING (OBJ_ACCESS_KEY,
                                         G_MKEY
                                        ) OBJ_ACCESS_KEY,
                          DECRYPTSTRING (OBJ_SA_ID, G_MKEY) OBJ_SA_ID,
                          DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                          DECRYPTSTRING (OBJ_FA_ID, G_MKEY) OBJ_FA_ID,
                          DECRYPTSTRING (OBJ_IA_ID, G_MKEY) OBJ_IA_ID,
                          DECRYPTSTRING (OBJ_UPDATED_BY,
                                         G_MKEY
                                        ) OBJ_UPDATED_BY,
                          DECRYPTSTRING (OBJ_UPDATED_DATE,
                                         G_MKEY
                                        ) OBJ_UPDATED_DATE
                     FROM IAS_OBJ_VIEWS
                    WHERE MOD_ID = G_MODULE)
            WHERE OBJ_TYPE IN ('WAITING', 'WAITING') AND OBJ_ID IS NOT NULL
         ORDER BY OBJ_ID;
   END;

   PROCEDURE CHECK_RELEAM (P_S_ID1 VARCHAR2, P_S_ID2 VARCHAR2, P_S_ID3 VARCHAR2)
   IS
      V_I             NUMBER;
      V_S1            VARCHAR2 (1000);
      V_S2            VARCHAR2 (1000);
      V_T             VARCHAR2 (1000);
      V_U             VARCHAR2 (1000);
      V_MAX_ACCESS    NUMBER;
      V_MAX_DATE      VARCHAR2 (100);
      V_CONTEXT_VAL   VARCHAR2 (1000);
      V_L_TYPE        NUMBER;
      V_L_USER        NUMBER;
      V_TRAIL_NO      NUMBER;
      V_TRAIL_LIMIT   NUMBER;
      V_TRAIL_DATE    DATE;
      V_ES            VARCHAR2 (1000);
      V_AID           VARCHAR2 (1000);
      V_LT            NUMBER (2);
   BEGIN
      V_LT :=
         SUBSTR (SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || G_MODULE),
                 1,
                 1
                );

      BEGIN
         SELECT OBJ_CREATED_BY, OBJ_CREATED_DATE, OBJ_ELEMENT_STATE
           INTO V_MAX_ACCESS, V_MAX_DATE, V_ES
           FROM (SELECT DECRYPTSTRING (OBJ_ELEMENT_STATE,
                                       G_MKEY
                                      ) OBJ_ELEMENT_STATE,
                        DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                        DECRYPTSTRING (OBJ_CREATED_DATE,
                                       G_MKEY
                                      ) OBJ_CREATED_DATE,
                        DECRYPTSTRING (OBJ_CREATED_BY, G_MKEY) OBJ_CREATED_BY,
                        DECRYPTSTRING (OBJ_UPDATED_BY, G_MKEY) OBJ_UPDATED_BY,
                        DECRYPTSTRING (OBJ_UPDATED_DATE,
                                       G_MKEY
                                      ) OBJ_UPDATED_DATE
                   FROM IAS_OBJ_VIEWS
                  WHERE MOD_ID = G_MODULE)
          WHERE OBJ_TYPE IN ('SERVER', 'ADMIN');

         V_L_TYPE := TO_NUMBER (SUBSTR (V_ES, 1, 1));
         V_L_USER := TO_NUMBER (SUBSTR (V_ES, 5, 4));
         G_MAX_USER := V_L_USER;
         V_TRAIL_NO := 0;
         V_MAX_ACCESS := 0;

         IF V_ES IS NULL OR LENGTH (V_ES) != '18'
         THEN
            G_LICENSE_TEXT :=
                  V_ES
               || ' Attempt to Compromize License Security , No License defination Found ....';
         END IF;

         IF     V_L_TYPE = 3
            AND V_MAX_DATE IS NOT NULL
            AND TO_DATE (V_MAX_DATE, 'ddmmrrrrhh24miss') < SYSDATE
         THEN
            G_LICENSE_TEXT :=
                  'Application License has expired by date ,Max Date is '
               || TO_CHAR (TO_DATE (V_MAX_DATE, 'ddmmrrrrhh24miss'),
                           'dd-mm-rrrr'
                          );
         END IF;

         IF V_L_TYPE = 3 AND TO_DATE (V_MAX_DATE, 'ddmmrrrrhh24miss') <
                                                                       SYSDATE
         THEN
            G_LICENSE_TEXT :=
                  'Application License has expired by date ,Max Date is '
               || TO_CHAR (TO_DATE (V_MAX_DATE, 'ddmmrrrrhh24miss'),
                           'dd-mm-rrrr'
                          );
         END IF;

         IF V_L_TYPE = 5 AND NVL (V_TRAIL_NO, 0) IS NULL
         THEN
            G_LICENSE_TEXT :=
                  'Attempt to Compromize License Security , Try to skip some access ....'
               || V_ES;
         END IF;

         IF G_ACCESS_TIMES IS NULL
         THEN
            BEGIN
               SELECT OBJ_UPDATED_BY, OBJ_UPDATED_DATE
                 INTO G_ACCESS_TIMES, G_ACCESS_DATE
                 FROM (SELECT DECRYPTSTRING
                                        (OBJ_ELEMENT_STATE,
                                         G_MKEY
                                        ) OBJ_ELEMENT_STATE,
                              DECRYPTSTRING (OBJ_ACCESS_ID1,
                                             G_MKEY
                                            ) OBJ_ACCESS_ID1,
                              DECRYPTSTRING (OBJ_ACCESS_ID2,
                                             G_MKEY
                                            ) OBJ_ACCESS_ID2,
                              DECRYPTSTRING (OBJ_ACCESS_ID3,
                                             G_MKEY
                                            ) OBJ_ACCESS_ID3,
                              DECRYPTSTRING
                                        (OBJ_DEVELOPER_KEY,
                                         G_MKEY
                                        ) OBJ_DEVELOPER_KEY,
                              DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
                              DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                              DECRYPTSTRING (OBJ_UPDATED_BY,
                                             G_MKEY
                                            ) OBJ_UPDATED_BY,
                              DECRYPTSTRING
                                          (OBJ_UPDATED_DATE,
                                           G_MKEY
                                          ) OBJ_UPDATED_DATE
                         FROM IAS_OBJ_VIEWS
                        WHERE MOD_ID = G_MODULE)
                WHERE (   (    NVL (OBJ_NAME, 'xx') != 'Empty'
                           AND V_LT = 1
                           AND (   SUBSTR (OBJ_ACCESS_ID1, 1, 12) =
                                                       SUBSTR (P_S_ID1, 1, 12)
                                OR SUBSTR (OBJ_ACCESS_ID2, 1, 100) =
                                                      SUBSTR (P_S_ID2, 1, 100)
                                OR SUBSTR (OBJ_ACCESS_ID3, 1, 100) =
                                                      SUBSTR (P_S_ID3, 1, 100)
                               )
                          )
                       OR (OBJ_TYPE IN ('SERVER', 'ADMIN') AND V_LT != 1)
                      );
            EXCEPTION
               WHEN NO_DATA_FOUND
               THEN
                  G_LICENSE_TEXT := 'WAITING';                  
                  RETURN;
            END;
         END IF;

         IF     V_L_TYPE = 3
            AND SYSDATE <
                   NVL (TO_DATE (G_ACCESS_DATE, 'dd-mm-rrrr-hh24:mi:ss'),
                        SYSDATE
                       )
         THEN
            G_LICENSE_TEXT :=
               'Application License has expired by date ,Try to reduce date ';
         END IF;

         IF V_L_TYPE = 5 AND NVL (G_ACCESS_TIMES, 0) > NVL (V_MAX_ACCESS, 0)
         THEN
            G_LICENSE_TEXT :=
                  'Application License has expired by Access times ,Access times limit is '
               || TO_CHAR (NVL (V_MAX_ACCESS, 0));
         END IF;
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            G_LICENSE_TEXT :=
               'Attempt to Compromize License Security , No Server or Client License Found ....';
         WHEN TOO_MANY_ROWS
         THEN
            G_LICENSE_TEXT :=
                  P_S_ID1
               || '---'
               || P_S_ID2
               || '   '
               || 'Attempt to Compromize License Security , Too Same Server or Client License Found ....';
      END;
   END;



   FUNCTION GET_DET_CREDIENTALS (P_SER IN VARCHAR2, P_MOD IN VARCHAR2)
      RETURN VARCHAR2
   IS
      V_RET   VARCHAR2 (2000);
      V_LT    NUMBER (2);
   BEGIN
      INIT_KEY;

      SELECT OBJ_ELEMENT_STATE
        INTO V_RET
        FROM (SELECT DECRYPTSTRING (OBJ_ELEMENT_STATE,
                                    G_MKEY
                                   ) OBJ_ELEMENT_STATE
                FROM IAS_OBJ_VIEW_DET
               WHERE MOD_ID = P_MOD AND SER_ID = P_SER);

      RETURN V_RET;
   EXCEPTION
      WHEN OTHERS
      THEN
         RETURN V_RET;
   END;



   FUNCTION GET_MY_CREDIENTALS (
      P_A1   IN   VARCHAR2,
      P_A2   IN   VARCHAR2,
      P_T    IN   VARCHAR2,
      P_U    IN   VARCHAR2
   )
      RETURN REC_TABLES
   IS
      V_RET   REC_TABLES;
      V_LT    NUMBER (2);
   BEGIN
      INIT_KEY;
      CHECK_RELEAM (G_S_ID, G_S_ID2, G_S_ID3);
      V_LT :=
         SUBSTR (SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || G_MODULE),
                 1,
                 1
                );

      IF SYS_CONTEXT ('ys_context', 'IAS_SYS') IS NOT NULL AND G_MKEY IS NULL
      THEN
         G_MKEY := SYS_CONTEXT ('ys_context', 'IAS_SYS');
      END IF;

      IF G_MKEY IS NULL
      THEN
         SELECT -1 OBJ_ID,
                'Onyx pro must be opened in the main serverXXX , to be available for clients ....'
                                                                     OBJ_TYPE,
                NULL OBJ_ACCESS_ID1, NULL OBJ_ACCESS_ID2, NULL OBJ_NAME,
                NULL OBJ_DEVELOPER_KEY, NULL OBJ_ACCESS_KEY,
                NULL OBJ_SA_ID, NULL OBJ_PA_ID, NULL OBJ_FA_ID,
                NULL OBJ_IA_ID, NULL OBJ_UPDATED_BY, NULL OBJ_UPDATED_DATE
           INTO V_RET.OBJ_ID,
                V_RET.OBJ_TYPE,
                V_RET.OBJ_ACCESS_ID1, V_RET.OBJ_ACCESS_ID2, V_RET.OBJ_NAME,
                V_RET.OBJ_DEVELOPER_KEY, V_RET.OBJ_ACCESS_KEY,
                V_RET.OBJ_SA_ID, V_RET.OBJ_PA_ID, V_RET.OBJ_FA_ID,
                V_RET.OBJ_IA_ID, V_RET.OBJ_UPDATED_BY, V_RET.OBJ_UPDATED_DATE
           FROM DUAL;
      ELSE

         SELECT   DECODE (OBJ_TYPE, 'WAITING', NULL, OBJ_ID) OBJ_ID,
                  DECODE (G_LICENSE_TEXT,
                          NULL, DECODE (V_LT, 1, OBJ_TYPE, 'SHARED'),
                          G_LICENSE_TEXT
                         ) OBJ_TYPE,
                  OBJ_ACCESS_ID1,
                  NVL (G_LIC_TEXT, OBJ_ACCESS_ID2) OBJ_ACCESS_ID2, OBJ_NAME,
                  OBJ_DEVELOPER_KEY,
                  NVL (G_DATA_SRC, OBJ_ACCESS_KEY) OBJ_ACCESS_KEY, OBJ_SA_ID,
                  OBJ_PA_ID, OBJ_FA_ID, OBJ_IA_ID,
                  OBJ_UPDATED_BY, OBJ_UPDATED_DATE
             INTO V_RET.OBJ_ID,
                  V_RET.OBJ_TYPE,
                  V_RET.OBJ_ACCESS_ID1,
                  V_RET.OBJ_ACCESS_ID2, V_RET.OBJ_NAME,
                  V_RET.OBJ_DEVELOPER_KEY,
                  V_RET.OBJ_ACCESS_KEY, V_RET.OBJ_SA_ID,
                  V_RET.OBJ_PA_ID, V_RET.OBJ_FA_ID, V_RET.OBJ_IA_ID,
                  V_RET.OBJ_UPDATED_BY, V_RET.OBJ_UPDATED_DATE
             FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID,
                          DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                          DECRYPTSTRING (OBJ_ACCESS_ID1,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID1,
                          DECRYPTSTRING (OBJ_ACCESS_ID2,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID22,
                          DECRYPTSTRING (OBJ_ACCESS_ID3,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID33,
                          DECRYPTSTRING (OBJ_ELEMENT_STATE,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID2,
                          DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
                          DECRYPTSTRING (OBJ_DEVELOPER_KEY,
                                         G_MKEY
                                        ) OBJ_DEVELOPER_KEY,
                          DECRYPTSTRING (OBJ_ACCESS_ID3,
                                         G_MKEY
                                        ) OBJ_ACCESS_KEY,
                          DECRYPTSTRING (OBJ_SA_ID, G_MKEY) OBJ_SA_ID,
                          DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                          DECRYPTSTRING (OBJ_FA_ID, G_MKEY) OBJ_FA_ID,
                          DECRYPTSTRING (OBJ_IA_ID, G_MKEY) OBJ_IA_ID,
                          DECRYPTSTRING (OBJ_BLOB_ID6, G_MKEY) OBJ_UPDATED_BY,
                          DECRYPTSTRING (OBJ_CREATED_DATE,
                                         G_MKEY
                                        ) OBJ_UPDATED_DATE
                     FROM IAS_OBJ_VIEWS
                    WHERE MOD_ID = G_MODULE)
            WHERE (   (    OBJ_TYPE IN
                                     ('CLIENT', 'SERVER', 'ADMIN', 'WAITING')
                       AND V_LT = 1
                       AND (   SUBSTR (OBJ_ACCESS_ID1, 1, 12) =
                                                          SUBSTR (P_A1, 1, 12)
                            OR SUBSTR (OBJ_ACCESS_ID22, 1, 100) =
                                                         SUBSTR (P_A2, 1, 100)
                            OR SUBSTR (OBJ_ACCESS_ID33, 1, 100) =
                                                      SUBSTR (G_S_ID3, 1, 100)
                           )
                      )
                   OR (OBJ_TYPE IN ('SERVER', 'ADMIN') AND V_LT != 1)
                  )
         ORDER BY OBJ_ID;
      END IF;

      RETURN V_RET;
   END;



   PROCEDURE QUERY3 (
      P_A1        IN       VARCHAR2,
      P_A2        IN       VARCHAR2,
      P_T         IN       VARCHAR2,
      P_U         IN       VARCHAR2,
      RESULTSET   IN OUT   REC_TABLES_REF
   )
   IS
      V_LT   NUMBER (2);
   BEGIN
      INIT_KEY;
      CHECK_RELEAM (G_S_ID, G_S_ID2, G_S_ID3);
      V_LT :=
         SUBSTR (SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || G_MODULE),
                 1,
                 1
                );

      IF SYS_CONTEXT ('ys_context', 'IAS_SYS') IS NOT NULL AND G_MKEY IS NULL
      THEN
         G_MKEY := SYS_CONTEXT ('ys_context', 'IAS_SYS');
      END IF;

      IF G_MKEY IS NULL
      THEN
         OPEN RESULTSET FOR
            SELECT -1 OBJ_ID,
                   'Onyx pro must be opened in the main server , to be available for clients ....'
                                                                    OBJ_TYPE,
                   NULL OBJ_ACCESS_ID1, NULL OBJ_ACCESS_ID2, NULL OBJ_NAME,
                   NULL OBJ_DEVELOPER_KEY, NULL OBJ_ACCESS_KEY,
                   NULL OBJ_SA_ID, NULL OBJ_PA_ID, NULL OBJ_FA_ID,
                   NULL OBJ_IA_ID, NULL OBJ_UPDATED_BY,
                   NULL OBJ_UPDATED_DATE
              FROM DUAL;
      ELSE

         OPEN RESULTSET FOR
            SELECT   DECODE (OBJ_TYPE, 'WAITING', NULL, OBJ_ID) OBJ_ID,
                     DECODE (G_LICENSE_TEXT,
                             NULL, DECODE (V_LT, 1, OBJ_TYPE, 'SHARED'),
                             G_LICENSE_TEXT
                            ) OBJ_TYPE,
                     OBJ_ACCESS_ID1,
                     NVL (G_LIC_TEXT, OBJ_ACCESS_ID2) OBJ_ACCESS_ID2,
                     OBJ_NAME, OBJ_DEVELOPER_KEY,
                     NVL (G_DATA_SRC, OBJ_ACCESS_KEY) OBJ_ACCESS_KEY,
                     OBJ_SA_ID, OBJ_PA_ID, OBJ_FA_ID, OBJ_IA_ID,
                     OBJ_UPDATED_BY, OBJ_UPDATED_DATE
                FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID,
                             DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                             DECRYPTSTRING (OBJ_ACCESS_ID1,
                                            G_MKEY
                                           ) OBJ_ACCESS_ID1,
                             DECRYPTSTRING (OBJ_ACCESS_ID2,
                                            G_MKEY
                                           ) OBJ_ACCESS_ID22,
                             DECRYPTSTRING (OBJ_ACCESS_ID3,
                                            G_MKEY
                                           ) OBJ_ACCESS_ID33,
                             DECRYPTSTRING (OBJ_ELEMENT_STATE,
                                            G_MKEY
                                           ) OBJ_ACCESS_ID2,
                             DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
                             DECRYPTSTRING
                                        (OBJ_DEVELOPER_KEY,
                                         G_MKEY
                                        ) OBJ_DEVELOPER_KEY,
                             DECRYPTSTRING (OBJ_ACCESS_ID3,
                                            G_MKEY
                                           ) OBJ_ACCESS_KEY,
                             DECRYPTSTRING (OBJ_SA_ID, G_MKEY) OBJ_SA_ID,
                             DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                             DECRYPTSTRING (OBJ_FA_ID, G_MKEY) OBJ_FA_ID,
                             DECRYPTSTRING (OBJ_IA_ID, G_MKEY) OBJ_IA_ID,
                             DECRYPTSTRING (OBJ_UPDATED_BY,
                                            G_MKEY
                                           ) OBJ_UPDATED_BY,
                             DECRYPTSTRING
                                          (OBJ_UPDATED_DATE,
                                           G_MKEY
                                          ) OBJ_UPDATED_DATE
                        FROM IAS_OBJ_VIEWS
                       WHERE MOD_ID = G_MODULE)
               WHERE (   (    OBJ_TYPE IN
                                     ('CLIENT', 'SERVER', 'ADMIN', 'WAITING')
                          AND V_LT = 1
                          AND (   SUBSTR (OBJ_ACCESS_ID1, 1, 12) =
                                                          SUBSTR (P_A1, 1, 12)
                               OR SUBSTR (OBJ_ACCESS_ID22, 1, 100) =
                                                         SUBSTR (P_A2, 1, 100)
                               OR SUBSTR (OBJ_ACCESS_ID33, 1, 100) =
                                                      SUBSTR (G_S_ID3, 1, 100)
                              )
                         )
                      OR (OBJ_TYPE IN ('SERVER') AND V_LT != 1)
                     )
            ORDER BY OBJ_ID;
      END IF;
   END;

   PROCEDURE QUERY_DET (RESULTSET IN OUT REC_TABLES_REF)
   IS
   BEGIN
      INIT_KEY;

      OPEN RESULTSET FOR
         SELECT   SER_ID OBJ_ID, OBJ_TYPE, OBJ_ACCESS_ID1, OBJ_ACCESS_ID2,
                  DECODE (OBJ_NAME, 'Empty', NULL, OBJ_NAME) OBJ_NAME,
                  DECODE (OBJ_DEVELOPER_KEY,
                          'Empty', NULL,
                          OBJ_DEVELOPER_KEY
                         ) OBJ_DEVELOPER_KEY,
                  OBJ_ACCESS_KEY, OBJ_SA_ID, OBJ_PA_ID, OBJ_FA_ID, OBJ_IA_ID,
                  OBJ_UPDATED_BY, OBJ_UPDATED_DATE
             FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID, SER_ID,
                          DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                          DECRYPTSTRING (OBJ_ACCESS_ID1,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID1,
                          DECRYPTSTRING (OBJ_ACCESS_ID2,
                                         G_MKEY
                                        ) OBJ_ACCESS_ID2,
                          DECRYPTSTRING (OBJ_BLOB_ID4, G_MKEY) OBJ_NAME,
                          DECRYPTSTRING (OBJ_BLOB_ID5,
                                         G_MKEY
                                        ) OBJ_DEVELOPER_KEY,
                          DECRYPTSTRING (OBJ_ACCESS_KEY,
                                         G_MKEY
                                        ) OBJ_ACCESS_KEY,
                          DECRYPTSTRING (OBJ_ELEMENT_STATE, G_MKEY) OBJ_SA_ID,
                          DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                          DECRYPTSTRING (OBJ_BLOB_ID2, G_MKEY) OBJ_FA_ID,
                          DECRYPTSTRING (OBJ_BLOB_ID3, G_MKEY) OBJ_IA_ID,
                          DECRYPTSTRING (OBJ_UPDATED_BY,
                                         G_MKEY
                                        ) OBJ_UPDATED_BY,
                          DECRYPTSTRING (OBJ_CREATED_DATE,
                                         G_MKEY
                                        ) OBJ_UPDATED_DATE
                     FROM IAS_OBJ_VIEW_DET
                    WHERE MOD_ID = G_MODULE)
            WHERE OBJ_TYPE IN ('SERVER', 'ADMIN', 'CLIENT', 'PENDING')
              AND OBJ_ID IS NOT NULL
         ORDER BY OBJ_NAME, OBJ_ID;
   END;

   PROCEDURE INIT_DETAILS (
      P_INIT_TYPE   VARCHAR2,
      P_SER         VARCHAR2,
      P_MOD         VARCHAR2,
      P_TOKEN       VARCHAR2,
      P_S_ID1       VARCHAR2,
      P_S_ID2       VARCHAR2,
      P_C_TN        VARCHAR2,
      P_C_UN        VARCHAR2,
      P_SOURCE_UN   VARCHAR2,
      P_L_STRING    VARCHAR2,
      P_L_UN        VARCHAR2,
      P_L_UN2       VARCHAR2,
      P_L_UN3       VARCHAR2
   )
   IS

      V_L_TYPE           NUMBER;
      V_L_USER           NUMBER;
      V_TRAIL_NO         NUMBER;
      V_TRAIL_LIMIT      NUMBER;
      V_TRAIL_DATE       DATE;
      V_OBJ_ACCESS_ID4   VARCHAR2 (2000);
      V_CONTEXT_VAL      VARCHAR2 (2000);
      V_L_UN             VARCHAR2 (1000);
      V_CURR_L_UN        VARCHAR2 (1000);
      V_CURR_L_UN2       VARCHAR2 (1000);
      V_CURR_L_UN3       VARCHAR2 (1000);
      V_CURR_LSE         NUMBER          := 0;
   BEGIN
      INIT_KEY;

      IF P_L_STRING IS NOT NULL
      THEN
         V_TRAIL_DATE := TO_DATE (P_L_STRING, 'DD-MM-RRRR');
      ELSE
         V_TRAIL_DATE := TO_DATE ('31-12-9999', 'DD-MM-RRRR');
      END IF;

      SELECT COUNT (*)
        INTO V_CURR_LSE
        FROM IAS_OBJ_VIEW_DET
       WHERE MOD_ID = P_MOD AND SER_ID = P_SER;

      IF NVL (V_CURR_LSE, 0) > 0
      THEN
         UPDATE IAS_OBJ_VIEW_DET
            SET OBJ_ELEMENT_STATE = ENCRYPTSTRING (P_L_UN, G_MKEY),
                OBJ_CREATED_DATE =
                   ENCRYPTSTRING (TO_CHAR (V_TRAIL_DATE, 'ddmmrrrrhh24miss'),
                                  G_MKEY
                                 ),
                OBJ_BLOB_ID5 =
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 )
          WHERE MOD_ID = P_MOD AND SER_ID = P_SER;

         UPDATE IAS_OBJ_VIEWS
            SET OBJ_ELEMENT_STATE = ENCRYPTSTRING (P_L_UN2, G_MKEY),
                OBJ_BLOB_ID2 = ENCRYPTSTRING (P_L_UN3, G_MKEY)
          WHERE MOD_ID = P_MOD;

         INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || P_MOD, P_L_UN2);
         COMMIT;
         RETURN;
      END IF;

      IF V_L_USER = 1
      THEN
         V_L_USER := 0;
      END IF;

      IF V_L_TYPE != 1
      THEN
         V_L_USER := 0;
      END IF;

      IF V_L_TYPE = 5
      THEN
         V_TRAIL_LIMIT := V_TRAIL_NO;
         V_TRAIL_DATE := ADD_MONTHS (SYSDATE, 9999999);
      ELSIF V_L_TYPE = 3
      THEN
         V_TRAIL_LIMIT := V_TRAIL_NO;
         V_TRAIL_DATE := ADD_MONTHS (SYSDATE, NVL (V_TRAIL_LIMIT, 1));
      END IF;

      V_OBJ_ACCESS_ID4 := ENCRYPTSTRING (P_S_ID1, SUBSTR (P_S_ID2, 1, 8));

      INSERT INTO IAS_OBJ_VIEW_DET
                  (OBJ_ID, MOD_ID, SER_ID,
                   OBJ_TYPE,
                   OBJ_ACCESS_ID1,
                   OBJ_ACCESS_ID2,
                   OBJ_NAME,
                   OBJ_DEVELOPER_KEY,
                   OBJ_ACCESS_KEY,
                   OBJ_ACCESS_ID3,
                   OBJ_DESCRIPTION,
                   OBJ_ELEMENT_STATE,
                   OBJ_BLOB_ID2,
                   OBJ_BLOB_ID3,
                   OBJ_BLOB_ID4,
                   OBJ_BLOB_ID5,
                   OBJ_SA_ID,
                   OBJ_PA_ID,
                   OBJ_FA_ID,
                   OBJ_IA_ID,
                   OBJ_CREATED_BY,
                   OBJ_CREATED_DATE,
                   OBJ_UPDATED_BY,
                   OBJ_UPDATED_DATE,
                   OBJ_ACCESS_ID4, OBJ_BLOB_ID1
                  )
           VALUES (ENCRYPTSTRING (0, G_MKEY), P_MOD, P_SER,
                   ENCRYPTSTRING ('SERVER', G_MKEY),
                   ENCRYPTSTRING (P_S_ID1, G_MKEY),
                   ENCRYPTSTRING (P_S_ID2, G_MKEY),
                   ENCRYPTSTRING (P_C_UN, G_MKEY),
                   ENCRYPTSTRING (P_C_TN, G_MKEY),
                   ENCRYPTSTRING (P_L_STRING, G_MKEY),
                   ENCRYPTSTRING (P_SOURCE_UN, G_MKEY),
                   ENCRYPTSTRING (   '00'
                                  || TO_CHAR (SYSDATE,
                                              'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING (P_L_UN, G_MKEY),
                   ENCRYPTSTRING (P_L_UN, G_MKEY),
                   ENCRYPTSTRING (P_L_UN, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING ('EUL_SA', G_MKEY),
                   ENCRYPTSTRING ('EUL_PA', G_MKEY),
                   ENCRYPTSTRING ('EUL_FA', G_MKEY),
                   ENCRYPTSTRING ('EUL_IA', G_MKEY),
                   ENCRYPTSTRING (V_TRAIL_LIMIT, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (V_TRAIL_DATE, 'ddmmrrrrhh24miss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING (0, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   V_OBJ_ACCESS_ID4, ENCRYPTSTRING (P_S_ID1, G_MKEY)
                  );

      UPDATE IAS_OBJ_VIEWS
         SET OBJ_ELEMENT_STATE = ENCRYPTSTRING (P_L_UN2, G_MKEY),
             OBJ_BLOB_ID2 = ENCRYPTSTRING (P_L_UN3, G_MKEY)
       WHERE MOD_ID = P_MOD;

      INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || P_MOD, P_L_UN2);
      COMMIT;
      COMMIT;
      NULL;
   END;

   PROCEDURE INIT_MAIN (
      P_TOKEN       VARCHAR2,
      P_S_ID1       VARCHAR2,
      P_S_ID2       VARCHAR2,
      P_C_TN        VARCHAR2,
      P_C_UN        VARCHAR2,
      P_SOURCE_UN   VARCHAR2,
      P_L_STRING    VARCHAR2,
      P_L_UN        VARCHAR2,
      P_L_ED        VARCHAR2,
      P_REACT       VARCHAR2,
      P_MOD_STR     VARCHAR2,
      P_ST          VARCHAR2 DEFAULT 'SERVER'
   )
   IS

      V_L_TYPE           NUMBER;
      V_L_USER           NUMBER;
      V_TRAIL_NO         NUMBER;
      V_TRAIL_LIMIT      NUMBER;
      V_TRAIL_DATE       DATE;
      V_OBJ_ACCESS_ID4   VARCHAR2 (2000);
      V_CONTEXT_VAL      VARCHAR2 (2000);
      V_L_UN             VARCHAR2 (1000);
      V_CURR_L_UN        VARCHAR2 (1000);
      V_CURR_L_UN2       VARCHAR2 (1000);
      V_CURR_LSE         NUMBER          := 0;
      V_SERVER_TYPE      VARCHAR2 (100)  := P_ST;
   
   BEGIN
      G_MKEY := SUBSTR (P_S_ID1, 1, 8);
      INIT_CONTEXT ('ys_context', P_TOKEN, P_S_ID1);
      INIT_CONTEXT ('ys_context', P_TOKEN || '.ds', P_SOURCE_UN);
      INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || G_MODULE, P_L_UN);
      INIT_CONTEXT ('ys_context', 'IAS_SYS' || '.st', V_SERVER_TYPE);
      INIT_CONTEXT ('ys_context',
                    'IAS_SYS' || '.sid',
                    UPPER (SYSTEM.GET_CONTEXT_VAL ('SESSIONID'))
                   );
      V_L_TYPE := TO_NUMBER (SUBSTR (P_L_UN, 1, 1));
      V_L_USER := TO_NUMBER (SUBSTR (P_L_UN, 5, 4));
      V_TRAIL_NO := 0;                    
      V_CURR_L_UN2 := SUBSTR (V_L_TYPE, 1, 1) || '00000000000000000';

      IF V_L_TYPE = 6
      THEN
         V_CURR_L_UN :=
                   SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || G_MODULE);
         V_L_UN :=
               SUBSTR (V_CURR_L_UN, 1, 2)
            || TO_CHAR (  TO_NUMBER (SUBSTR (P_L_UN, 3, 4))
                        + TO_NUMBER (SUBSTR (V_CURR_L_UN, 3, 4)),
                        'fm0999'
                       )
            || SUBSTR (V_CURR_L_UN, 7, 1)
            || TO_CHAR (  TO_NUMBER (SUBSTR (P_L_UN, 8, 2))
                        + TO_NUMBER (SUBSTR (V_CURR_L_UN, 8, 2)),
                        'fm09'
                       )
            || TO_CHAR (  TO_NUMBER (SUBSTR (P_L_UN, 10, 2))
                        + TO_NUMBER (SUBSTR (V_CURR_L_UN, 10, 2)),
                        'fm09'
                       )
            || TO_CHAR (  TO_NUMBER (SUBSTR (P_L_UN, 12, 2))
                        + TO_NUMBER (SUBSTR (V_CURR_L_UN, 12, 2)),
                        'fm09'
                       );

         UPDATE IAS_OBJ_VIEWS
            SET OBJ_ELEMENT_STATE = ENCRYPTSTRING (V_L_UN, G_MKEY)
          WHERE MOD_ID = G_MODULE;

         INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || G_MODULE, V_L_UN);
         COMMIT;
         RETURN;
      END IF;

      IF P_REACT IS NOT NULL
      THEN
         V_TRAIL_DATE := TO_DATE (P_REACT, 'DD-MM-RRRR');
      ELSE
         V_TRAIL_DATE := TO_DATE ('31-12-9999', 'DD-MM-RRRR');
      END IF;

      SELECT COUNT (*)
        INTO V_CURR_LSE
        FROM IAS_OBJ_VIEWS
       WHERE MOD_ID = G_MODULE;

      IF NVL (V_CURR_LSE, 0) > 0
      THEN
         G_MKEY := SUBSTR (P_S_ID1, 1, 8);
         INIT_CONTEXT ('ys_context', P_TOKEN, P_S_ID1);
         INIT_CONTEXT ('ys_context', P_TOKEN || '.ds', P_SOURCE_UN);
         INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || G_MODULE, P_L_UN);

         UPDATE IAS_OBJ_VIEWS
            SET OBJ_ELEMENT_STATE = ENCRYPTSTRING (P_L_UN, G_MKEY),
                OBJ_BLOB_ID5 =
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                OBJ_BLOB_ID6 = ENCRYPTSTRING (P_MOD_STR, G_MKEY),
                OBJ_CREATED_DATE =
                   ENCRYPTSTRING (TO_CHAR (V_TRAIL_DATE, 'ddmmrrrrhh24miss'),
                                  G_MKEY
                                 )
          WHERE MOD_ID = G_MODULE;

         COMMIT;
         RETURN;
      END IF;

      IF V_L_TYPE NOT IN (6, 7, 8)
      THEN
         G_MKEY := SUBSTR (P_S_ID1, 1, 8);
         INIT_CONTEXT ('ys_context', P_TOKEN, P_S_ID1);
         INIT_CONTEXT ('ys_context', P_TOKEN || '.ds', P_SOURCE_UN);
         INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || G_MODULE, P_L_UN);
      END IF;

      IF V_L_USER = 1
      THEN
         V_L_USER := 0;
      END IF;

      IF V_L_TYPE != 1
      THEN
         V_L_USER := 0;
      END IF;

      IF V_L_TYPE = 5
      THEN
         V_TRAIL_LIMIT := V_TRAIL_NO;
         V_TRAIL_DATE := ADD_MONTHS (SYSDATE, 9999999);
      ELSIF V_L_TYPE = 3
      THEN
         V_TRAIL_LIMIT := V_TRAIL_NO;
         V_TRAIL_DATE := ADD_MONTHS (SYSDATE, NVL (V_TRAIL_LIMIT, 1));
      END IF;

      IF P_REACT IS NOT NULL
      THEN
         V_TRAIL_DATE := TO_DATE (P_REACT, 'DD-MM-RRRR');
      ELSE
         V_TRAIL_DATE := TO_DATE ('31-12-9999', 'DD-MM-RRRR');
      END IF;

      V_OBJ_ACCESS_ID4 := ENCRYPTSTRING (P_S_ID1, SUBSTR (P_S_ID2, 1, 8));

      INSERT INTO IAS_OBJ_VIEWS
                  (OBJ_BLOB_ID6,
                   OBJ_ID,
                   OBJ_TYPE,
                   OBJ_ACCESS_ID1,
                   OBJ_ACCESS_ID2,
                   OBJ_NAME,
                   OBJ_DEVELOPER_KEY,
                   OBJ_ACCESS_KEY,
                   OBJ_ACCESS_ID3,
                   OBJ_DESCRIPTION,
                   OBJ_ELEMENT_STATE,
                   OBJ_BLOB_ID2,
                   OBJ_BLOB_ID3,
                   OBJ_BLOB_ID4,
                   OBJ_BLOB_ID5,
                   OBJ_SA_ID,
                   OBJ_PA_ID,
                   OBJ_FA_ID,
                   OBJ_IA_ID,
                   OBJ_CREATED_BY,
                   OBJ_CREATED_DATE,
                   OBJ_UPDATED_BY,
                   OBJ_UPDATED_DATE,
                   OBJ_ACCESS_ID4, OBJ_BLOB_ID1,
                   MOD_ID
                  )
           VALUES (ENCRYPTSTRING (P_MOD_STR, G_MKEY),
                   ENCRYPTSTRING (G_MODULE, G_MKEY),
                   ENCRYPTSTRING (V_SERVER_TYPE, G_MKEY),
                   ENCRYPTSTRING (P_S_ID1, G_MKEY),
                   ENCRYPTSTRING (P_S_ID2, G_MKEY),
                   ENCRYPTSTRING (P_C_UN, G_MKEY),
                   ENCRYPTSTRING (P_C_TN, G_MKEY),
                   ENCRYPTSTRING (P_L_STRING, G_MKEY),
                   ENCRYPTSTRING (P_SOURCE_UN, G_MKEY),
                   ENCRYPTSTRING (   '00'
                                  || TO_CHAR (SYSDATE,
                                              'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING (P_L_UN, G_MKEY),
                   ENCRYPTSTRING (V_CURR_L_UN2, G_MKEY),
                   ENCRYPTSTRING (P_L_UN, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING ('EUL_SA', G_MKEY),
                   ENCRYPTSTRING ('EUL_PA', G_MKEY),
                   ENCRYPTSTRING ('EUL_FA', G_MKEY),
                   ENCRYPTSTRING ('EUL_IA', G_MKEY),
                   ENCRYPTSTRING (V_TRAIL_LIMIT, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (V_TRAIL_DATE, 'ddmmrrrrhh24miss'),
                                  G_MKEY
                                 ),
                   ENCRYPTSTRING (0, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   V_OBJ_ACCESS_ID4, ENCRYPTSTRING (P_S_ID1, G_MKEY),
                   NVL (G_MODULE, 0)
                  );

      FOR I IN 1 .. NVL (V_L_USER, 1)
      LOOP
         INSERT INTO IAS_OBJ_VIEWS
                     (OBJ_ID,
                      OBJ_TYPE,
                      OBJ_ACCESS_ID1,
                      OBJ_ACCESS_ID2,
                      OBJ_NAME,
                      OBJ_DEVELOPER_KEY,
                      OBJ_ACCESS_KEY,
                      OBJ_ACCESS_ID3,
                      OBJ_DESCRIPTION,
                      OBJ_ELEMENT_STATE,
                      OBJ_SA_ID,
                      OBJ_PA_ID,
                      OBJ_FA_ID,
                      OBJ_IA_ID,
                      OBJ_CREATED_BY,
                      OBJ_CREATED_DATE,
                      OBJ_UPDATED_BY,
                      OBJ_UPDATED_DATE,
                      OBJ_ACCESS_ID4, OBJ_BLOB_ID1, MOD_ID
                     )
              VALUES (ENCRYPTSTRING (I, G_MKEY),
                      ENCRYPTSTRING ('PENDING', G_MKEY),
                      ENCRYPTSTRING (P_S_ID1, G_MKEY),
                      ENCRYPTSTRING (P_S_ID2, G_MKEY),
                      ENCRYPTSTRING ('Empty', G_MKEY),
                      ENCRYPTSTRING ('Empty', G_MKEY),
                      ENCRYPTSTRING (P_L_STRING, G_MKEY),
                      ENCRYPTSTRING (P_SOURCE_UN, G_MKEY),
                      ENCRYPTSTRING (   '00'
                                     || TO_CHAR (SYSDATE,
                                                 'dd-mm-rrrr-hh24:mi:ss'
                                                ),
                                     G_MKEY
                                    ),
                      ENCRYPTSTRING (P_L_UN, G_MKEY),
                      ENCRYPTSTRING ('EUL_SA', G_MKEY),
                      ENCRYPTSTRING ('EUL_PA', G_MKEY),
                      ENCRYPTSTRING ('EUL_FA', G_MKEY),
                      ENCRYPTSTRING ('EUL_IA', G_MKEY),
                      ENCRYPTSTRING (V_TRAIL_LIMIT, G_MKEY),
                      ENCRYPTSTRING (TO_CHAR (V_TRAIL_DATE,
                                              'ddmmrrrrhh24miss'),
                                     G_MKEY
                                    ),
                      ENCRYPTSTRING (0, G_MKEY),
                      ENCRYPTSTRING (TO_CHAR (SYSDATE,
                                              'dd-mm-rrrr-hh24:mi:ss'),
                                     G_MKEY
                                    ),
                      NULL, ENCRYPTSTRING (P_S_ID1, G_MKEY), G_MODULE
                     );
      END LOOP;

      COMMIT;
      NULL;
   END;

   PROCEDURE INIT_MAIN5 (P_C_ID1 NUMBER, P_C_ID2 NUMBER)
   IS
   BEGIN
      INIT_KEY;

      UPDATE IAS_OBJ_VIEWS
         SET OBJ_TYPE = ENCRYPTSTRING ('PENDING', G_MKEY),
             OBJ_ACCESS_ID1 = NULL,
             OBJ_ACCESS_ID2 = NULL,
             OBJ_NAME = NULL,
             OBJ_SA_ID = NULL,
             OBJ_DEVELOPER_KEY = NULL
       WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_C_ID1 AND MOD_ID = G_MODULE;

      COMMIT;
      NULL;
   END;

   PROCEDURE INIT_MAIN2 (
      P_C_ID1   VARCHAR2,
      P_C_ID2   VARCHAR2,
      P_C_ID3   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   )
   IS
      V_I    NUMBER;
      V_DS   VARCHAR2 (100);
   BEGIN
      INIT_KEY;

      SELECT NVL (MAX (OBJ_ID), 0) + 1
        INTO V_I
        FROM (SELECT TO_NUMBER (DECRYPTSTRING (OBJ_ID, G_MKEY)) OBJ_ID
                FROM IAS_OBJ_VIEWS
               WHERE MOD_ID = G_MODULE);

      INSERT INTO IAS_OBJ_VIEWS
                  (OBJ_ID,
                   OBJ_TYPE,
                   OBJ_ACCESS_ID1,
                   OBJ_ACCESS_ID2,
                   OBJ_ACCESS_ID3,
                   OBJ_ACCESS_ID4,
                   OBJ_NAME,
                   OBJ_DEVELOPER_KEY, OBJ_ACCESS_KEY,
                   OBJ_DESCRIPTION,
                   OBJ_ELEMENT_STATE, OBJ_SA_ID, OBJ_PA_ID, OBJ_FA_ID,
                   OBJ_IA_ID, OBJ_CREATED_BY, OBJ_CREATED_DATE,
                   OBJ_UPDATED_BY,
                   OBJ_UPDATED_DATE
                  )
           VALUES (ENCRYPTSTRING (V_I, G_MKEY),
                   ENCRYPTSTRING ('WAITING', G_MKEY),
                   ENCRYPTSTRING (P_C_ID1, G_MKEY),
                   ENCRYPTSTRING (P_C_ID2, G_MKEY),
                   ENCRYPTSTRING (P_C_ID3, G_MKEY),
                   ENCRYPTSTRING (P_C_ID1, SUBSTR (P_C_ID2, 1, 8)),
                   ENCRYPTSTRING (P_C_UN, G_MKEY),
                   ENCRYPTSTRING (P_C_TN, G_MKEY), NULL,
                   ENCRYPTSTRING (   V_I
                                  || '0'
                                  || TO_CHAR (SYSDATE,
                                              'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                   NULL, NULL, NULL, NULL,
                   NULL, NULL, NULL,
                   ENCRYPTSTRING (0, G_MKEY),
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 )
                  );

      COMMIT;
   END;

   PROCEDURE UPDATE_REC (
      P_ID   IN   VARCHAR2,
      P_T    IN   VARCHAR2,
      P_U    IN   VARCHAR2,
      P_SA        VARCHAR2,
      P_FA        VARCHAR2,
      P_PA        VARCHAR2,
      P_IA        VARCHAR2
   )
   IS
   BEGIN
      INIT_KEY;

      UPDATE IAS_OBJ_VIEWS
         SET OBJ_NAME = ENCRYPTSTRING (P_U, G_MKEY),
             OBJ_DEVELOPER_KEY = ENCRYPTSTRING (P_T, G_MKEY),
             OBJ_SA_ID = ENCRYPTSTRING (P_SA, G_MKEY),
             OBJ_PA_ID = ENCRYPTSTRING (P_PA, G_MKEY),
             OBJ_FA_ID = ENCRYPTSTRING (P_FA, G_MKEY),
             OBJ_IA_ID = ENCRYPTSTRING (P_IA, G_MKEY)
       WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_ID AND MOD_ID = G_MODULE;

      COMMIT;
   END;

   PROCEDURE UPDATE_SOURCE (P_ID IN VARCHAR2, P_S IN VARCHAR2)
   IS
   BEGIN
      INIT_KEY;

      UPDATE IAS_OBJ_VIEWS
         SET OBJ_ACCESS_ID3 = ENCRYPTSTRING (P_S, G_MKEY)
       WHERE MOD_ID = G_MODULE;

      G_DATA_SRC := P_S;
      INIT_CONTEXT ('ys_context', G_TOKEN || '.ds', P_S);
      COMMIT;
   END;

   PROCEDURE REGISTER_ACCESS (P_ID IN VARCHAR2, P_T IN VARCHAR2)
   IS
      V_DESC   VARCHAR2 (2000);
      V_LD     VARCHAR2 (2000);
      V_LA     VARCHAR2 (2000);
   BEGIN
      INIT_KEY;
      G_ACCESS_TIMES := NVL (G_ACCESS_TIMES, 0) + 1;

      IF SYSDATE <
              NVL (TO_DATE (G_ACCESS_DATE, 'dd-mm-rrrr-hh24:mi:ss'), SYSDATE)
      THEN
         G_LICENSE_TEXT :=
               'Application License has expired by date ,Try to reduce date ';
      END IF;

      G_ACCESS_DATE := TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss');

      BEGIN
         SELECT OBJ_DESCRIPTION, OBJ_UPDATED_BY, OBJ_UPDATED_DATE
           INTO V_DESC, V_LA, V_LD
           FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID,
                        DECRYPTSTRING (OBJ_DESCRIPTION,
                                       G_MKEY
                                      ) OBJ_DESCRIPTION,
                        DECRYPTSTRING (OBJ_UPDATED_BY, G_MKEY) OBJ_UPDATED_BY,
                        DECRYPTSTRING (OBJ_UPDATED_DATE,
                                       G_MKEY
                                      ) OBJ_UPDATED_DATE
                   FROM IAS_OBJ_VIEWS
                  WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_ID
                    AND MOD_ID = G_MODULE);
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            G_LICENSE_TEXT :=
               'Attempt to Compromize License Security , Miss License uses no found....';
         WHEN TOO_MANY_ROWS
         THEN
            G_LICENSE_TEXT :=
               'Attempt to Compromize License Security , Miss License uses too many ....';
      END;

      IF V_DESC IS NULL OR V_LA IS NULL OR V_LD IS NULL
      THEN
         G_LICENSE_TEXT :=
            'Attempt to Compromize License Security , Miss License uses ....';
      ELSE
         NULL;

         UPDATE IAS_OBJ_VIEWS
            SET OBJ_UPDATED_BY =
                   ENCRYPTSTRING (  NVL (DECRYPTSTRING (OBJ_UPDATED_BY,
                                                        G_MKEY),
                                         0
                                        )
                                  + 1,
                                  G_MKEY
                                 ),
                OBJ_UPDATED_DATE =
                   ENCRYPTSTRING (TO_CHAR (SYSDATE, 'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 ),
                OBJ_DESCRIPTION =
                   ENCRYPTSTRING (   P_ID
                                  || NVL (DECRYPTSTRING (OBJ_UPDATED_BY,
                                                         G_MKEY
                                                        ),
                                          0
                                         )
                                  +  1
                                  || TO_CHAR (SYSDATE,
                                              'dd-mm-rrrr-hh24:mi:ss'),
                                  G_MKEY
                                 )
          WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_ID AND MOD_ID = G_MODULE;
      END IF;

      COMMIT;
   END;

   PROCEDURE INIT_MAIN3 (P_C_ID1 NUMBER, P_C_ID2 NUMBER)
   IS
      V_ID1            VARCHAR2 (2000);
      V_ID2            VARCHAR2 (2000);
      V_ID3            VARCHAR2 (2000);
      V_ID4            VARCHAR2 (2000);
      V_TN             VARCHAR2 (2000);
      V_UN             VARCHAR2 (2000);
      V_OBJ_BLOB_ID1   VARCHAR2 (2000);
   BEGIN
      INIT_KEY;

      SELECT DECRYPTSTRING (OBJ_ACCESS_ID1, G_MKEY) OBJ_ACCESS_ID1,
             DECRYPTSTRING (OBJ_ACCESS_ID2, G_MKEY) OBJ_ACCESS_ID2,
             DECRYPTSTRING (OBJ_ACCESS_ID3, G_MKEY) OBJ_ACCESS_ID3,
             DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
             DECRYPTSTRING (OBJ_DEVELOPER_KEY, G_MKEY) OBJ_DEVELOPER_KEY,
             DECRYPTSTRING (OBJ_BLOB_ID1, G_MKEY) OBJ_BLOB_ID1
        INTO V_ID1,
             V_ID2,
             V_ID3,
             V_UN,
             V_TN,
             V_OBJ_BLOB_ID1
        FROM IAS_OBJ_VIEWS
       WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_C_ID1 AND MOD_ID = G_MODULE;

      UPDATE IAS_OBJ_VIEWS
         SET OBJ_TYPE = ENCRYPTSTRING ('CLIENT', G_MKEY),
             OBJ_ACCESS_ID1 = ENCRYPTSTRING (V_ID1, G_MKEY),
             OBJ_ACCESS_ID2 = ENCRYPTSTRING (V_ID2, G_MKEY),
             OBJ_ACCESS_ID3 = ENCRYPTSTRING (V_ID3, G_MKEY),
             OBJ_ACCESS_ID4 = ENCRYPTSTRING (V_ID1, SUBSTR (V_ID2, 1, 8)),
             OBJ_BLOB_ID1 =
                   ENCRYPTSTRING (SUBSTR (G_MKEY, 1, 8), SUBSTR (V_ID1, 1, 8)),
             OBJ_NAME = ENCRYPTSTRING (V_UN, G_MKEY),
             OBJ_DEVELOPER_KEY = ENCRYPTSTRING (V_TN, G_MKEY),
             
             OBJ_SA_ID = ENCRYPTSTRING ('EUL_SA', G_MKEY)
       WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_C_ID2 AND MOD_ID = G_MODULE;

      DELETE      IAS_OBJ_VIEWS
            WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_C_ID1
                  AND MOD_ID = G_MODULE;

      COMMIT;
      NULL;
   END;

   PROCEDURE INIT_MAIN4 (P_C_ID1 NUMBER, P_C_ID2 NUMBER)
   IS
   BEGIN
      INIT_KEY;

      DELETE      IAS_OBJ_VIEWS
            WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = P_C_ID1
              AND MOD_ID = G_MODULE;

      COMMIT;
      NULL;
   END;



   PROCEDURE INIT_RELEAM2 (
      P_TOKEN   VARCHAR2,
      P_S_ID1   VARCHAR2,
      P_S_ID2   VARCHAR2,
      P_S_ID3   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   )
   IS
      V_I             NUMBER;
      V_S3            VARCHAR2 (1000);
      V_ES            VARCHAR2 (1000);
      V_T             VARCHAR2 (1000);
      V_U             VARCHAR2 (1000);
      V_CONTEXT_VAL   VARCHAR2 (1000);
   BEGIN
      V_CONTEXT_VAL := SYS_CONTEXT ('ys_context', P_TOKEN);
      G_DATA_SRC := SYS_CONTEXT ('ys_context', P_TOKEN || '.ds');
      G_LIC_TEXT := SYS_CONTEXT ('ys_context', P_TOKEN || '.lt' || G_MODULE);
      G_TOKEN := P_TOKEN;
      G_S_ID := P_S_ID1;                             
      G_S_ID2 := P_S_ID2;
      G_S_ID3 := P_S_ID3;
      G_T := P_C_TN;
      G_U := P_C_UN;

      IF V_CONTEXT_VAL IS NOT NULL
      THEN
         RETURN;
      END IF;

      NULL;
      G_MKEY := SUBSTR (P_S_ID1, 1, 8);

      BEGIN
         SELECT OBJ_ACCESS_ID3, OBJ_ELEMENT_STATE
           INTO V_S3, V_ES
           FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID,
                        DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                        DECRYPTSTRING (OBJ_ACCESS_ID3, G_MKEY) OBJ_ACCESS_ID3,
                        DECRYPTSTRING (OBJ_ELEMENT_STATE,
                                       G_MKEY
                                      ) OBJ_ELEMENT_STATE,
                        DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
                        DECRYPTSTRING (OBJ_DEVELOPER_KEY,
                                       G_MKEY
                                      ) OBJ_DEVELOPER_KEY,
                        DECRYPTSTRING (OBJ_ACCESS_KEY, G_MKEY) OBJ_ACCESS_KEY,
                        DECRYPTSTRING (OBJ_SA_ID, G_MKEY) OBJ_SA_ID,
                        DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                        DECRYPTSTRING (OBJ_FA_ID, G_MKEY) OBJ_FA_ID,
                        DECRYPTSTRING (OBJ_IA_ID, G_MKEY) OBJ_IA_ID,
                        DECRYPTSTRING (OBJ_UPDATED_BY, G_MKEY) OBJ_UPDATED_BY,
                        DECRYPTSTRING (OBJ_UPDATED_DATE,
                                       G_MKEY
                                      ) OBJ_UPDATED_DATE
                   FROM IAS_OBJ_VIEWS
                  WHERE MOD_ID = G_MODULE)
          WHERE OBJ_TYPE IN ('SERVER', 'ADMIN')
            AND UPPER (P_C_UN) = UPPER (OBJ_NAME)
            AND UPPER (P_C_TN) = UPPER (OBJ_DEVELOPER_KEY);

         INIT_CONTEXT ('ys_context', P_TOKEN, G_MKEY);
         INIT_CONTEXT ('ys_context', P_TOKEN || '.ds', V_S3);
         INIT_CONTEXT ('ys_context', P_TOKEN || '.lt' || G_MODULE, V_ES);
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            NULL;
            G_TOKEN := P_TOKEN;
            G_MKEY := SYS_CONTEXT ('ys_context', P_TOKEN);
      END;
   END;



   FUNCTION GVAL (P_ID VARCHAR2, P_KEY VARCHAR2 DEFAULT NULL)
      RETURN VARCHAR2
   IS
   BEGIN
      RETURN INIT_PKG.DECRYPTSTRING (P_ID,
                                     NVL (P_KEY,
                                          NVL (G_MKEY,
                                               SYS_CONTEXT ('ys_context',
                                                            'IAS_SYS'
                                                           )
                                              )
                                         )
                                    );
   END;

   PROCEDURE INIT_CONTEXT (P_CONTEXT VARCHAR2, P_PRED VARCHAR2, P_VAL VARCHAR2)
   IS
   BEGIN
      NULL;
      DBMS_SESSION.SET_CONTEXT (P_CONTEXT, P_PRED, P_VAL);
   END;

   PROCEDURE INIT_CONTEXT2 (
      P_CONTEXT   VARCHAR2,
      P_PRED      VARCHAR2,
      P_VAL       VARCHAR2,
      P_CLIENT    VARCHAR2
   )
   IS
   BEGIN
      NULL;
      DBMS_SESSION.SET_CONTEXT (P_CONTEXT, P_PRED, P_VAL);
   END;


   PROCEDURE INIT_RELEAMXX (
      P_TOKEN   VARCHAR2,
      P_S_ID1   VARCHAR2,
      P_S_ID2   VARCHAR2,
      P_S_ID3   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   )
   IS
      XX   VARCHAR2 (2000);
   BEGIN
      SELECT INIT_PKG.GVAL (OBJ_ACCESS_ID4, SUBSTR (P_S_ID1, 1, 8))
                                                               OBJ_ACCESS_ID4
        INTO XX
        FROM IAS_OBJ_VIEWS
       WHERE INIT_PKG.GVAL (OBJ_ACCESS_ID4, SUBSTR (P_S_ID1, 1, 8)) IS NOT NULL
         AND INIT_PKG.GVAL (OBJ_TYPE,
                            INIT_PKG.GVAL (OBJ_ACCESS_ID4,
                                           SUBSTR (P_S_ID1, 1, 8)
                                          )
                           ) IN ('SERVER', 'ADMIN');

      INIT_RELEAM (P_TOKEN, XX, P_S_ID2, P_S_ID3, P_C_TN, P_C_UN);
   EXCEPTION
      WHEN NO_DATA_FOUND
      THEN
         NULL;   
      WHEN TOO_MANY_ROWS
      THEN
         NULL;   
   END;


   PROCEDURE INIT_RELEAM_OTHERS (
      P_MODULE   VARCHAR2,
      P_TOKEN    VARCHAR2,
      P_S_ID1    VARCHAR2,
      P_S_ID2    VARCHAR2,
      P_S_ID3    VARCHAR2,
      P_C_TN     VARCHAR2,
      P_C_UN     VARCHAR2
   )
   IS
      CURSOR XX
      IS
         SELECT MOD_ID
           FROM IAS_OBJ_VIEWS
          WHERE MOD_ID != P_MODULE;

      V_MODULE   VARCHAR2 (200);
   BEGIN
      V_MODULE := P_MODULE;
      G_SETUP_FOR_ALL := TRUE;

      FOR REC IN XX
      LOOP
         IF SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || REC.MOD_ID) IS NULL
         THEN
            SET_MODULE (REC.MOD_ID);
            INIT_RELEAM (P_TOKEN, P_S_ID1, P_S_ID2, P_S_ID3, P_C_TN, P_C_UN);
         END IF;
      END LOOP;

      SET_MODULE (V_MODULE);
      NULL;
   END;


   PROCEDURE INIT_RELEAM (
      P_TOKEN   VARCHAR2,
      P_S_ID1   VARCHAR2,
      P_S_ID2   VARCHAR2,
      P_S_ID3   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   )
   IS
      V_I             NUMBER;
      V_S3            VARCHAR2 (1000);
      V_ES            VARCHAR2 (1000);
      V_T             VARCHAR2 (1000);
      V_U             VARCHAR2 (1000);
      V_CONTEXT_VAL   VARCHAR2 (1000);
      VK1             VARCHAR2 (1000);
      VK2             VARCHAR2 (1000);
      VKEFF           VARCHAR2 (1000);
      V_ID            VARCHAR2 (1000);
      V_ST            VARCHAR2 (2000);
      V_SID           NUMBER;
   BEGIN
      V_CONTEXT_VAL := SYS_CONTEXT ('ys_context', 'IAS_SYS');
      G_DATA_SRC := SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.ds');
      G_LIC_TEXT :=
                   SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || G_MODULE);
      G_ALLOW_PC_ADMIN :=
                        NVL (SYS_CONTEXT ('ys_context', 'allow_pc_admin'), 0);
      G_OPERATION_MODE :=
                        NVL (SYS_CONTEXT ('ys_context', 'operation_mode'), 3);
      G_TOKEN := 'IAS_SYS';
      G_S_ID := P_S_ID1;                             
      G_S_ID2 := P_S_ID2;
      G_S_ID3 := P_S_ID3;
      G_T := P_C_TN;
      G_U := P_C_UN;

      IF     G_OPERATION_MODE IN (1, 2, 3, 4)
         AND (   (P_S_ID1 IS NOT NULL AND P_S_ID2 IS NULL)
              OR (P_S_ID1 IS NULL AND P_S_ID2 IS NOT NULL)
             )
      THEN
         G_LICENSE_TEXT := 'Missing keys ...';
      END IF;

      IF V_CONTEXT_VAL IS NOT NULL AND G_MKEY IS NULL
      THEN
         G_MKEY := V_CONTEXT_VAL;
      END IF;

      V_ST := SYS_CONTEXT ('ys_context', 'IAS_SYS' || '.st');

      IF V_CONTEXT_VAL IS NOT NULL AND G_LIC_TEXT IS NOT NULL
      THEN
         RETURN;
      END IF;

      NULL;
      INIT_CONTEXT ('ys_context', 'IAS_SYS', SUBSTR (P_S_ID1, 1, 12));
      G_MKEY := SUBSTR (P_S_ID1, 1, 8);

      BEGIN
         SELECT OBJ_ACCESS_ID3, OBJ_ELEMENT_STATE, OBJ_ACCESS_ID1,
                OBJ_ACCESS_ID2, OBJ_BLOB_ID1, OBJ_ID, OBJ_TYPE
           INTO V_S3, V_ES, VK1,
                VK2, VKEFF, V_ID, V_ST
           FROM (SELECT DECRYPTSTRING (OBJ_ID, G_MKEY) OBJ_ID,
                        DECRYPTSTRING (OBJ_TYPE, G_MKEY) OBJ_TYPE,
                        DECRYPTSTRING (OBJ_ACCESS_ID1, G_MKEY) OBJ_ACCESS_ID1,
                        DECRYPTSTRING (OBJ_ACCESS_ID2,
                                       G_MKEY) OBJ_ACCESS_ID22,
                        DECRYPTSTRING (OBJ_ACCESS_ID3,
                                       G_MKEY) OBJ_ACCESS_ID33,
                        DECRYPTSTRING (OBJ_ACCESS_ID3, G_MKEY) OBJ_ACCESS_ID3,
                        DECRYPTSTRING (OBJ_ACCESS_ID2, G_MKEY) OBJ_ACCESS_ID2,
                        DECRYPTSTRING (OBJ_BLOB_ID1, G_MKEY) OBJ_BLOB_ID1,
                        DECRYPTSTRING (OBJ_ELEMENT_STATE,
                                       G_MKEY
                                      ) OBJ_ELEMENT_STATE,
                        DECRYPTSTRING (OBJ_NAME, G_MKEY) OBJ_NAME,
                        DECRYPTSTRING (OBJ_DEVELOPER_KEY,
                                       G_MKEY
                                      ) OBJ_DEVELOPER_KEY,
                        DECRYPTSTRING (OBJ_ACCESS_KEY, G_MKEY) OBJ_ACCESS_KEY,
                        DECRYPTSTRING (OBJ_SA_ID, G_MKEY) OBJ_SA_ID,
                        DECRYPTSTRING (OBJ_PA_ID, G_MKEY) OBJ_PA_ID,
                        DECRYPTSTRING (OBJ_FA_ID, G_MKEY) OBJ_FA_ID,
                        DECRYPTSTRING (OBJ_IA_ID, G_MKEY) OBJ_IA_ID,
                        DECRYPTSTRING (OBJ_UPDATED_BY, G_MKEY) OBJ_UPDATED_BY,
                        DECRYPTSTRING (OBJ_UPDATED_DATE,
                                       G_MKEY
                                      ) OBJ_UPDATED_DATE
                   FROM IAS_OBJ_VIEWS
                  WHERE MOD_ID = G_MODULE)
          WHERE OBJ_TYPE IN ('SERVER', 'ADMIN')
            AND (   SUBSTR (OBJ_ACCESS_ID1, 1, 12) = SUBSTR (P_S_ID1, 1, 12)
                 OR SUBSTR (OBJ_ACCESS_ID22, 1, 100) =
                                                      SUBSTR (P_S_ID2, 1, 100)
                 OR SUBSTR (OBJ_ACCESS_ID33, 1, 100) =
                                                      SUBSTR (P_S_ID3, 1, 100)
                );

         INIT_CONTEXT ('ys_context', 'IAS_SYS' || '.st', V_ST);
         INIT_CONTEXT ('ys_context',
                       'IAS_SYS' || '.sid',
                       UPPER (SYSTEM.GET_CONTEXT_VAL ('SESSIONID'))
                      );
         INIT_CONTEXT ('ys_context', 'IAS_SYS', SUBSTR (VKEFF, 1, 12));
         G_MKEY := SUBSTR (VKEFF, 1, 8);

         IF NOT G_SETUP_FOR_ALL
         THEN
            INIT_RELEAM_OTHERS (G_MODULE,
                                P_TOKEN,
                                P_S_ID1,
                                P_S_ID2,
                                P_S_ID3,
                                P_C_TN,
                                P_C_UN
                               );
         END IF;

         IF G_OPERATION_MODE = 3
         THEN
            IF    SUBSTR (P_S_ID1, 1, 12) != SUBSTR (VK1, 1, 12)
               OR SUBSTR (P_S_ID2, 1, 100) != SUBSTR (VK2, 1, 100)
            THEN
               INIT_PKG.CLEAR_CONTEXT ('ys_context', 'IAS_SYS');
               NULL;
               RETURN;
            END IF;
         END IF;

         IF     G_OPERATION_MODE IN (1, 2)
            AND NOT G_LOOP_BACK
            AND (SUBSTR (VK1, 1, 12) != SUBSTR (P_S_ID1, 1, 12))
         THEN
            UPDATE IAS_OBJ_VIEWS
               SET OBJ_ACCESS_ID1 = ENCRYPTSTRING (P_S_ID1, G_MKEY)
             WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = V_ID AND MOD_ID = G_MODULE;

            COMMIT;
         END IF;

         IF     G_OPERATION_MODE IN (1, 2)
            AND G_LOOP_BACK
            AND (SUBSTR (VK1, 1, 12) != SUBSTR (G_LOOP_ID, 1, 12))
         THEN
            UPDATE IAS_OBJ_VIEWS
               SET OBJ_ACCESS_ID1 = ENCRYPTSTRING (G_LOOP_ID, G_MKEY)
             WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = V_ID AND MOD_ID = G_MODULE;

            COMMIT;
         END IF;

         IF     G_OPERATION_MODE IN (1, 2)
            AND SUBSTR (VK2, 1, 100) != SUBSTR (P_S_ID2, 1, 100)
         THEN
            UPDATE IAS_OBJ_VIEWS
               SET OBJ_ACCESS_ID2 = ENCRYPTSTRING (P_S_ID2, G_MKEY)
             WHERE DECRYPTSTRING (OBJ_ID, G_MKEY) = V_ID AND MOD_ID = G_MODULE;

            COMMIT;
         END IF;

         INIT_CONTEXT ('ys_context', 'IAS_SYS' || '.ds',
                       NVL (V_S3, 'IAS_SYS'));
         INIT_CONTEXT ('ys_context', 'IAS_SYS' || '.lt' || G_MODULE, V_ES);
      EXCEPTION
         WHEN NO_DATA_FOUND
         THEN
            NULL;
            INIT_CONTEXT ('ys_context', 'IAS_SYS', NULL);
            G_TOKEN := P_TOKEN;
            G_MKEY := SYS_CONTEXT ('ys_context', 'IAS_SYS');

            IF G_OPERATION_MODE IN (0, 1, 2)
            THEN
               G_LOOP_BACK := TRUE;
               G_LOOP_ID := P_S_ID1;
               INIT_RELEAMXX (P_TOKEN,
                              P_S_ID2,
                              P_S_ID2,
                              P_S_ID3,
                              P_C_TN,
                              P_C_UN
                             );
            END IF;
      END;
   END;

   FUNCTION GETKEY
      RETURN VARCHAR2
   IS
   BEGIN
      RETURN G_MKEY;
   END;



   PROCEDURE SUBSCRIBE (
      P_S_ID1   VARCHAR2,
      P_S_ID2   VARCHAR2,
      P_S_ID3   VARCHAR2,
      P_S_ID4   VARCHAR2,
      P_C_TN    VARCHAR2,
      P_C_UN    VARCHAR2
   )
   IS
   BEGIN
      G_S_ID := P_S_ID1;
      G_S_ID2 := P_S_ID2;
      G_S_ID3 := P_S_ID3;
      G_S_ID4 := P_S_ID4;
      G_T := P_C_TN;
      G_U := P_C_UN;
   END;



   PROCEDURE CLEAR_CONTEXT (P_CONTEXT VARCHAR2, P_PRED VARCHAR2)
   IS
      CURSOR XX
      IS
         SELECT MOD_ID
           FROM IAS_OBJ_VIEWS;
   BEGIN
      INIT_PKG.INIT_CONTEXT (P_CONTEXT, P_PRED, NULL);
      INIT_PKG.INIT_CONTEXT ('ys_context', P_PRED || '.ds', NULL);
      INIT_PKG.INIT_CONTEXT ('ys_context', P_PRED || '.lt' || G_MODULE, NULL);
      INIT_PKG.INIT_CONTEXT ('ys_context', P_PRED || '.st', NULL);
      INIT_PKG.INIT_CONTEXT ('ys_context', P_PRED || '.sid', NULL);
      INIT_PKG.INIT_CONTEXT ('ys_context', P_PRED || '.err', NULL);

      FOR REC IN XX
      LOOP
         INIT_PKG.INIT_CONTEXT ('ys_context',
                                P_PRED || '.lt' || REC.MOD_ID,
                                NULL
                               );
         NULL;
      END LOOP;
   END;



   PROCEDURE SET_MODULE (P_MODULE VARCHAR2)
   IS
   BEGIN
      NULL;
      G_MODULE := P_MODULE;
   END;



   FUNCTION GETVERSION
      RETURN VARCHAR2
   IS
   BEGIN
      RETURN '1.1';
   END;


END INIT_PKG_new;
/
