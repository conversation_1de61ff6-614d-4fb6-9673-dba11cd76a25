<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit();
}

// Include configuration and database connection
require_once 'config.php';
require_once 'database.php';

class ActivationAPI {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new OracleDatabase();
        $this->logger = new Logger();
    }
    
    public function processActivation() {
        try {
            // Get and validate input data
            $input = $this->getInputData();
            $validationResult = $this->validateInput($input);
            
            if (!$validationResult['valid']) {
                return $this->errorResponse($validationResult['message']);
            }
            
            // Connect to database
            if (!$this->db->connect()) {
                $this->logger->error('Database connection failed');
                return $this->errorResponse('فشل في الاتصال بقاعدة البيانات');
            }
            
            // Check if system is already activated
            $existingActivation = $this->checkExistingActivation($input);
            if ($existingActivation) {
                return $this->handleExistingActivation($input, $existingActivation);
            }
            
            // Perform activation
            $activationResult = $this->performActivation($input);
            
            if ($activationResult['success']) {
                $this->logger->info('System activated successfully', $input);
                return $this->successResponse('تم تفعيل النظام بنجاح', $activationResult['data']);
            } else {
                $this->logger->error('Activation failed: ' . $activationResult['message'], $input);
                return $this->errorResponse($activationResult['message']);
            }
            
        } catch (Exception $e) {
            $this->logger->error('Activation exception: ' . $e->getMessage());
            return $this->errorResponse('حدث خطأ غير متوقع في النظام');
        } finally {
            $this->db->disconnect();
        }
    }
    
    private function getInputData() {
        $json = file_get_contents('php://input');
        $data = json_decode($json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON data');
        }
        
        return $data;
    }
    
    private function validateInput($input) {
        $required = ['token', 'serverId1', 'serverId2', 'clientName', 'clientUsername', 
                    'sourceUsername', 'licenseNumber', 'serverType'];
        
        foreach ($required as $field) {
            if (empty($input[$field])) {
                return ['valid' => false, 'message' => "الحقل {$field} مطلوب"];
            }
        }
        
        // Validate license number format (18 digits)
        if (!preg_match('/^[0-9]{18}$/', $input['licenseNumber'])) {
            return ['valid' => false, 'message' => 'رقم الترخيص يجب أن يكون 18 رقم'];
        }
        
        // Validate server type
        $validServerTypes = ['SERVER', 'CLIENT', 'ADMIN'];
        if (!in_array($input['serverType'], $validServerTypes)) {
            return ['valid' => false, 'message' => 'نوع الخادم غير صحيح'];
        }
        
        // Validate token format
        if (!preg_match('/^[A-Za-z0-9_]+$/', $input['token'])) {
            return ['valid' => false, 'message' => 'رمز التفعيل يحتوي على أحرف غير صحيحة'];
        }
        
        return ['valid' => true];
    }
    
    private function checkExistingActivation($input) {
        $sql = "BEGIN 
                    IAS_SYS.INIT_PKG_NEW.QUERY3(:p_a1, :p_a2, :p_t, :p_u, :resultset);
                END;";
        
        $stmt = $this->db->prepare($sql);
        $cursor = $this->db->getNewCursor();
        
        $stmt->bindParam(':p_a1', $input['serverId1']);
        $stmt->bindParam(':p_a2', $input['serverId2']);
        $stmt->bindParam(':p_t', $input['serverType']);
        $stmt->bindParam(':p_u', $input['clientUsername']);
        $stmt->bindParam(':resultset', $cursor, PDO::PARAM_STMT);
        
        if ($stmt->execute()) {
            $result = $cursor->fetchAll(PDO::FETCH_ASSOC);
            return !empty($result) ? $result[0] : null;
        }
        
        return null;
    }
    
    private function handleExistingActivation($input, $existing) {
        // Check if it's a valid existing activation
        if ($existing['OBJ_TYPE'] === 'SERVER' || $existing['OBJ_TYPE'] === 'ADMIN') {
            return $this->successResponse('النظام مفعل مسبقاً', [
                'status' => 'already_activated',
                'type' => $existing['OBJ_TYPE'],
                'activated_date' => $existing['OBJ_UPDATED_DATE']
            ]);
        } elseif ($existing['OBJ_TYPE'] === 'WAITING') {
            // Update existing waiting activation
            return $this->updateActivation($input);
        } else {
            return $this->errorResponse('حالة التفعيل غير صحيحة: ' . $existing['OBJ_TYPE']);
        }
    }
    
    private function performActivation($input) {
        // Set module context
        $this->setModule($input['token']);
        
        // Prepare activation parameters
        $params = $this->prepareActivationParams($input);
        
        // Call INIT_MAIN procedure
        $sql = "BEGIN 
                    IAS_SYS.INIT_PKG_NEW.INIT_MAIN(
                        :p_token, :p_s_id1, :p_s_id2, :p_c_tn, :p_c_un,
                        :p_source_un, :p_l_string, :p_l_un, :p_l_ed,
                        :p_react, :p_mod_str, :p_st
                    );
                END;";
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        if ($stmt->execute()) {
            // Verify activation by checking the result
            $verification = $this->verifyActivation($input);
            if ($verification['success']) {
                return [
                    'success' => true,
                    'data' => [
                        'activation_id' => $verification['activation_id'],
                        'status' => 'activated',
                        'type' => $input['serverType'],
                        'activated_date' => date('Y-m-d H:i:s')
                    ]
                ];
            } else {
                return ['success' => false, 'message' => $verification['message']];
            }
        } else {
            $errorInfo = $stmt->errorInfo();
            return ['success' => false, 'message' => 'فشل في تنفيذ التفعيل: ' . $errorInfo[2]];
        }
    }
    
    private function updateActivation($input) {
        // Similar to performActivation but for updates
        $params = $this->prepareActivationParams($input);
        
        $sql = "BEGIN 
                    IAS_SYS.INIT_PKG_NEW.INIT_MAIN(
                        :p_token, :p_s_id1, :p_s_id2, :p_c_tn, :p_c_un,
                        :p_source_un, :p_l_string, :p_l_un, :p_l_ed,
                        :p_react, :p_mod_str, :p_st
                    );
                END;";
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($params as $key => $value) {
            $stmt->bindParam($key, $value);
        }
        
        if ($stmt->execute()) {
            return $this->successResponse('تم تحديث التفعيل بنجاح', [
                'status' => 'updated',
                'type' => $input['serverType']
            ]);
        } else {
            $errorInfo = $stmt->errorInfo();
            return $this->errorResponse('فشل في تحديث التفعيل: ' . $errorInfo[2]);
        }
    }
    
    private function setModule($token) {
        $sql = "BEGIN IAS_SYS.INIT_PKG_NEW.SET_MODULE(:module); END;";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':module', $token);
        $stmt->execute();
    }
    
    private function prepareActivationParams($input) {
        // Format expiry date
        $expiryDate = !empty($input['expiryDate']) ? 
            date('d-m-Y', strtotime($input['expiryDate'])) : 
            '31-12-9999';
        
        return [
            ':p_token' => $input['token'],
            ':p_s_id1' => $input['serverId1'],
            ':p_s_id2' => $input['serverId2'],
            ':p_c_tn' => $input['clientName'],
            ':p_c_un' => $input['clientUsername'],
            ':p_source_un' => $input['sourceUsername'],
            ':p_l_string' => $input['licenseString'] ?? '',
            ':p_l_un' => $input['licenseNumber'],
            ':p_l_ed' => '', // License edition
            ':p_react' => $expiryDate,
            ':p_mod_str' => $input['moduleString'] ?? 'IAS_MODULE',
            ':p_st' => $input['serverType']
        ];
    }
    
    private function verifyActivation($input) {
        // Check if activation was successful by querying the system
        $credentials = $this->getCredentials($input);
        
        if ($credentials && $credentials['OBJ_TYPE'] !== 'WAITING') {
            return [
                'success' => true,
                'activation_id' => $credentials['OBJ_ID'],
                'message' => 'تم التحقق من التفعيل بنجاح'
            ];
        }
        
        return [
            'success' => false,
            'message' => 'فشل في التحقق من التفعيل'
        ];
    }
    
    private function getCredentials($input) {
        $sql = "BEGIN 
                    :result := IAS_SYS.INIT_PKG_NEW.GET_MY_CREDIENTALS(:p_a1, :p_a2, :p_t, :p_u);
                END;";
        
        $stmt = $this->db->prepare($sql);
        $result = null;
        
        $stmt->bindParam(':result', $result, PDO::PARAM_STMT);
        $stmt->bindParam(':p_a1', $input['serverId1']);
        $stmt->bindParam(':p_a2', $input['serverId2']);
        $stmt->bindParam(':p_t', $input['serverType']);
        $stmt->bindParam(':p_u', $input['clientUsername']);
        
        if ($stmt->execute()) {
            return $result;
        }
        
        return null;
    }
    
    private function successResponse($message, $data = null) {
        $response = [
            'success' => true,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if ($data) {
            $response['data'] = $data;
        }
        
        return $response;
    }
    
    private function errorResponse($message) {
        return [
            'success' => false,
            'message' => $message,
            'timestamp' => date('Y-m-d H:i:s')
        ];
    }
}

// Process the activation request
try {
    $api = new ActivationAPI();
    $result = $api->processActivation();
    echo json_encode($result, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في الخادم',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}
?>
