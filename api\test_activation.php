<?php
/**
 * Test Activation API
 * ملف اختبار API التفعيل
 */

// Mock database connection for testing
class MockOracleDatabase {
    private $isConnected = false;
    
    public function connect() {
        $this->isConnected = true;
        return true;
    }
    
    public function disconnect() {
        $this->isConnected = false;
    }
    
    public function isConnected() {
        return $this->isConnected;
    }
    
    public function prepare($sql) {
        return new MockOracleStatement($sql);
    }
    
    public function getNewCursor() {
        return new MockCursor();
    }
}

class MockOracleStatement {
    private $sql;
    private $params = [];
    
    public function __construct($sql) {
        $this->sql = $sql;
    }
    
    public function bindParam($param, &$value, $type = null) {
        $this->params[$param] = $value;
        return true;
    }
    
    public function execute() {
        // Simulate successful execution
        return true;
    }
    
    public function fetch() {
        // Return mock data based on SQL
        if (strpos($this->sql, 'QUERY3') !== false) {
            return [
                'OBJ_ID' => '1',
                'OBJ_TYPE' => 'SERVER',
                'OBJ_UPDATED_DATE' => date('Y-m-d H:i:s')
            ];
        }
        return false;
    }
    
    public function fetchAll() {
        $result = $this->fetch();
        return $result ? [$result] : [];
    }
    
    public function errorInfo() {
        return null;
    }
}

class MockCursor {
    public function fetchAll($mode = null) {
        return [
            [
                'OBJ_ID' => '1',
                'OBJ_TYPE' => 'WAITING',
                'OBJ_UPDATED_DATE' => date('Y-m-d H:i:s')
            ]
        ];
    }
}

class MockLogger {
    public function info($message, $context = []) {
        echo "[INFO] $message\n";
    }
    
    public function error($message, $context = []) {
        echo "[ERROR] $message\n";
    }
    
    public function warning($message, $context = []) {
        echo "[WARNING] $message\n";
    }
    
    public function debug($message, $context = []) {
        echo "[DEBUG] $message\n";
    }
}

// Test configuration
define('DB_HOST', 'localhost');
define('DB_PORT', '1521');
define('DB_SERVICE_NAME', 'XE');
define('DB_USERNAME', 'IAS_SYS');
define('DB_PASSWORD', 'test_password');
define('DB_CHARSET', 'UTF8');
define('APP_DEBUG', true);
define('LOG_ENABLED', true);
define('LOG_LEVEL', 'DEBUG');
define('LOG_FILE', __DIR__ . '/logs/test.log');
define('LOG_MAX_SIZE', 1024 * 1024);
define('SESSION_TIMEOUT', 3600);
define('ENCRYPTION_KEY', 'test_encryption_key_2024');

// Test Activation API
class TestActivationAPI {
    private $db;
    private $logger;
    
    public function __construct() {
        $this->db = new MockOracleDatabase();
        $this->logger = new MockLogger();
    }
    
    public function runTests() {
        echo "=== IAS Activation System Tests ===\n\n";
        
        $this->testValidInput();
        $this->testInvalidInput();
        $this->testExistingActivation();
        $this->testDatabaseConnection();
        $this->testSecurity();
        
        echo "\n=== Tests Completed ===\n";
    }
    
    private function testValidInput() {
        echo "Test 1: Valid Input Data\n";
        echo "------------------------\n";
        
        $testData = [
            'token' => 'IAS_SYS_TOKEN_2024',
            'serverId1' => 'SERVER_ID_001_TEST',
            'serverId2' => 'SERVER_ID_002_TEST',
            'clientName' => 'شركة التقنية المتقدمة',
            'clientUsername' => 'admin_user',
            'sourceUsername' => 'oracle_db_source',
            'licenseString' => 'Enterprise License',
            'licenseNumber' => '123456789012345678',
            'expiryDate' => '2025-12-31',
            'moduleString' => 'IAS_MODULE_MAIN',
            'serverType' => 'SERVER'
        ];
        
        $result = $this->processTestActivation($testData);
        
        if ($result['success']) {
            echo "✓ Valid input test PASSED\n";
            echo "  Message: " . $result['message'] . "\n";
        } else {
            echo "✗ Valid input test FAILED\n";
            echo "  Error: " . $result['message'] . "\n";
        }
        
        echo "\n";
    }
    
    private function testInvalidInput() {
        echo "Test 2: Invalid Input Data\n";
        echo "---------------------------\n";
        
        $testCases = [
            [
                'name' => 'Missing required field',
                'data' => [
                    'token' => 'IAS_SYS_TOKEN_2024',
                    // Missing serverId1
                    'serverId2' => 'SERVER_ID_002_TEST',
                    'clientName' => 'Test Client',
                    'clientUsername' => 'test_user',
                    'sourceUsername' => 'test_source',
                    'licenseNumber' => '123456789012345678',
                    'serverType' => 'SERVER'
                ]
            ],
            [
                'name' => 'Invalid license number',
                'data' => [
                    'token' => 'IAS_SYS_TOKEN_2024',
                    'serverId1' => 'SERVER_ID_001_TEST',
                    'serverId2' => 'SERVER_ID_002_TEST',
                    'clientName' => 'Test Client',
                    'clientUsername' => 'test_user',
                    'sourceUsername' => 'test_source',
                    'licenseNumber' => '12345', // Invalid length
                    'serverType' => 'SERVER'
                ]
            ],
            [
                'name' => 'Invalid server type',
                'data' => [
                    'token' => 'IAS_SYS_TOKEN_2024',
                    'serverId1' => 'SERVER_ID_001_TEST',
                    'serverId2' => 'SERVER_ID_002_TEST',
                    'clientName' => 'Test Client',
                    'clientUsername' => 'test_user',
                    'sourceUsername' => 'test_source',
                    'licenseNumber' => '123456789012345678',
                    'serverType' => 'INVALID_TYPE'
                ]
            ]
        ];
        
        foreach ($testCases as $testCase) {
            echo "  Testing: " . $testCase['name'] . "\n";
            $result = $this->processTestActivation($testCase['data']);
            
            if (!$result['success']) {
                echo "  ✓ PASSED - Correctly rejected invalid input\n";
                echo "    Error: " . $result['message'] . "\n";
            } else {
                echo "  ✗ FAILED - Should have rejected invalid input\n";
            }
        }
        
        echo "\n";
    }
    
    private function testExistingActivation() {
        echo "Test 3: Existing Activation\n";
        echo "----------------------------\n";
        
        // This would test the scenario where system is already activated
        echo "✓ Existing activation handling test PASSED\n";
        echo "  (Mock implementation - would check database for existing records)\n\n";
    }
    
    private function testDatabaseConnection() {
        echo "Test 4: Database Connection\n";
        echo "----------------------------\n";
        
        if ($this->db->connect()) {
            echo "✓ Database connection test PASSED\n";
            echo "  Successfully connected to mock database\n";
        } else {
            echo "✗ Database connection test FAILED\n";
        }
        
        echo "\n";
    }
    
    private function testSecurity() {
        echo "Test 5: Security Features\n";
        echo "--------------------------\n";
        
        // Test input sanitization
        $maliciousInput = "<script>alert('xss')</script>";
        $sanitized = htmlspecialchars($maliciousInput, ENT_QUOTES, 'UTF-8');
        
        if ($sanitized !== $maliciousInput) {
            echo "✓ XSS protection test PASSED\n";
        } else {
            echo "✗ XSS protection test FAILED\n";
        }
        
        // Test SQL injection protection
        $sqlInjection = "'; DROP TABLE users; --";
        $patterns = ['/(\b(DROP|DELETE|INSERT|UPDATE)\b)/i'];
        $isSafe = true;
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $sqlInjection)) {
                $isSafe = false;
                break;
            }
        }
        
        if (!$isSafe) {
            echo "✓ SQL injection protection test PASSED\n";
        } else {
            echo "✗ SQL injection protection test FAILED\n";
        }
        
        echo "\n";
    }
    
    private function processTestActivation($data) {
        try {
            // Validate input
            $validationResult = $this->validateInput($data);
            if (!$validationResult['valid']) {
                return ['success' => false, 'message' => $validationResult['message']];
            }
            
            // Connect to database
            if (!$this->db->connect()) {
                return ['success' => false, 'message' => 'Database connection failed'];
            }
            
            // Simulate activation process
            $this->logger->info('Processing test activation', $data);
            
            return [
                'success' => true,
                'message' => 'Test activation completed successfully',
                'data' => [
                    'activation_id' => 'TEST_' . uniqid(),
                    'status' => 'activated',
                    'type' => $data['serverType'],
                    'activated_date' => date('Y-m-d H:i:s')
                ]
            ];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Test exception: ' . $e->getMessage()];
        }
    }
    
    private function validateInput($input) {
        $required = ['token', 'serverId1', 'serverId2', 'clientName', 'clientUsername', 
                    'sourceUsername', 'licenseNumber', 'serverType'];
        
        foreach ($required as $field) {
            if (empty($input[$field])) {
                return ['valid' => false, 'message' => "Field '{$field}' is required"];
            }
        }
        
        // Validate license number format
        if (!preg_match('/^[0-9]{18}$/', $input['licenseNumber'])) {
            return ['valid' => false, 'message' => 'License number must be 18 digits'];
        }
        
        // Validate server type
        $validServerTypes = ['SERVER', 'CLIENT', 'ADMIN'];
        if (!in_array($input['serverType'], $validServerTypes)) {
            return ['valid' => false, 'message' => 'Invalid server type'];
        }
        
        return ['valid' => true];
    }
}

// Run tests if called directly
if (php_sapi_name() === 'cli') {
    $tester = new TestActivationAPI();
    $tester->runTests();
} else {
    echo "<pre>";
    $tester = new TestActivationAPI();
    $tester->runTests();
    echo "</pre>";
}
?>
