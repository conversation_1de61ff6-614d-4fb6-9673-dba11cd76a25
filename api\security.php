<?php
/**
 * Security and Encryption Class
 * فئة الأمان والتشفير
 */

class SecurityManager {
    private $encryptionKey;
    private $algorithm = 'AES-256-CBC';
    private $hashAlgorithm = 'sha256';
    
    public function __construct() {
        $this->encryptionKey = ENCRYPTION_KEY;
    }
    
    /**
     * Encrypt string using AES-256-CBC
     * @param string $data
     * @param string $key Optional custom key
     * @return string
     */
    public function encrypt($data, $key = null) {
        $key = $key ?: $this->encryptionKey;
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($this->algorithm));
        $encrypted = openssl_encrypt($data, $this->algorithm, $key, 0, $iv);
        
        return base64_encode($iv . $encrypted);
    }
    
    /**
     * Decrypt string using AES-256-CBC
     * @param string $data
     * @param string $key Optional custom key
     * @return string|false
     */
    public function decrypt($data, $key = null) {
        $key = $key ?: $this->encryptionKey;
        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length($this->algorithm);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);
        
        return openssl_decrypt($encrypted, $this->algorithm, $key, 0, $iv);
    }
    
    /**
     * Generate secure hash
     * @param string $data
     * @param string $salt
     * @return string
     */
    public function hash($data, $salt = '') {
        return hash($this->hashAlgorithm, $data . $salt);
    }
    
    /**
     * Verify hash
     * @param string $data
     * @param string $hash
     * @param string $salt
     * @return bool
     */
    public function verifyHash($data, $hash, $salt = '') {
        return hash_equals($hash, $this->hash($data, $salt));
    }
    
    /**
     * Generate random token
     * @param int $length
     * @return string
     */
    public function generateToken($length = 32) {
        return bin2hex(random_bytes($length / 2));
    }
    
    /**
     * Generate secure password
     * @param int $length
     * @return string
     */
    public function generatePassword($length = 12) {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        $password = '';
        
        for ($i = 0; $i < $length; $i++) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $password;
    }
    
    /**
     * Validate input against XSS
     * @param string $input
     * @return string
     */
    public function sanitizeInput($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * Validate SQL injection
     * @param string $input
     * @return bool
     */
    public function validateSqlInjection($input) {
        $patterns = [
            '/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/i',
            '/(\b(OR|AND)\s+\d+\s*=\s*\d+)/i',
            '/(\'|\"|;|--|\*|\/\*|\*\/)/i'
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return false;
            }
        }
        
        return true;
    }
}

/**
 * Rate Limiting Class
 */
class RateLimiter {
    private $storage = [];
    private $maxAttempts;
    private $timeWindow;
    
    public function __construct($maxAttempts = 100, $timeWindow = 3600) {
        $this->maxAttempts = $maxAttempts;
        $this->timeWindow = $timeWindow;
    }
    
    /**
     * Check if request is allowed
     * @param string $identifier
     * @return bool
     */
    public function isAllowed($identifier) {
        $now = time();
        $key = $this->getKey($identifier);
        
        // Clean old entries
        $this->cleanup($now);
        
        if (!isset($this->storage[$key])) {
            $this->storage[$key] = [];
        }
        
        // Count requests in time window
        $requests = array_filter($this->storage[$key], function($timestamp) use ($now) {
            return ($now - $timestamp) < $this->timeWindow;
        });
        
        if (count($requests) >= $this->maxAttempts) {
            return false;
        }
        
        // Add current request
        $this->storage[$key][] = $now;
        
        return true;
    }
    
    /**
     * Get remaining attempts
     * @param string $identifier
     * @return int
     */
    public function getRemainingAttempts($identifier) {
        $now = time();
        $key = $this->getKey($identifier);
        
        if (!isset($this->storage[$key])) {
            return $this->maxAttempts;
        }
        
        $requests = array_filter($this->storage[$key], function($timestamp) use ($now) {
            return ($now - $timestamp) < $this->timeWindow;
        });
        
        return max(0, $this->maxAttempts - count($requests));
    }
    
    /**
     * Reset attempts for identifier
     * @param string $identifier
     */
    public function reset($identifier) {
        $key = $this->getKey($identifier);
        unset($this->storage[$key]);
    }
    
    private function getKey($identifier) {
        return 'rate_limit_' . hash('sha256', $identifier);
    }
    
    private function cleanup($now) {
        foreach ($this->storage as $key => $timestamps) {
            $this->storage[$key] = array_filter($timestamps, function($timestamp) use ($now) {
                return ($now - $timestamp) < $this->timeWindow;
            });
            
            if (empty($this->storage[$key])) {
                unset($this->storage[$key]);
            }
        }
    }
}

/**
 * Session Security Manager
 */
class SessionSecurity {
    
    public function __construct() {
        $this->startSecureSession();
    }
    
    /**
     * Start secure session
     */
    private function startSecureSession() {
        if (session_status() === PHP_SESSION_NONE) {
            // Configure secure session settings
            ini_set('session.cookie_httponly', 1);
            ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
            ini_set('session.use_strict_mode', 1);
            ini_set('session.cookie_samesite', 'Strict');
            
            session_start();
            
            // Regenerate session ID periodically
            if (!isset($_SESSION['created'])) {
                $_SESSION['created'] = time();
            } elseif (time() - $_SESSION['created'] > 1800) { // 30 minutes
                session_regenerate_id(true);
                $_SESSION['created'] = time();
            }
        }
    }
    
    /**
     * Set session value
     * @param string $key
     * @param mixed $value
     */
    public function set($key, $value) {
        $_SESSION[$key] = $value;
    }
    
    /**
     * Get session value
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    public function get($key, $default = null) {
        return isset($_SESSION[$key]) ? $_SESSION[$key] : $default;
    }
    
    /**
     * Remove session value
     * @param string $key
     */
    public function remove($key) {
        unset($_SESSION[$key]);
    }
    
    /**
     * Destroy session
     */
    public function destroy() {
        session_destroy();
        $_SESSION = [];
    }
    
    /**
     * Check if session is valid
     * @return bool
     */
    public function isValid() {
        if (!isset($_SESSION['created'])) {
            return false;
        }
        
        // Check session timeout
        if (time() - $_SESSION['created'] > SESSION_TIMEOUT) {
            $this->destroy();
            return false;
        }
        
        return true;
    }
    
    /**
     * Generate CSRF token
     * @return string
     */
    public function generateCSRFToken() {
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        return $token;
    }
    
    /**
     * Verify CSRF token
     * @param string $token
     * @return bool
     */
    public function verifyCSRFToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
}

/**
 * Input Validator
 */
class InputValidator {
    
    /**
     * Validate email
     * @param string $email
     * @return bool
     */
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    /**
     * Validate URL
     * @param string $url
     * @return bool
     */
    public static function validateURL($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
    
    /**
     * Validate IP address
     * @param string $ip
     * @return bool
     */
    public static function validateIP($ip) {
        return filter_var($ip, FILTER_VALIDATE_IP) !== false;
    }
    
    /**
     * Validate string length
     * @param string $string
     * @param int $min
     * @param int $max
     * @return bool
     */
    public static function validateLength($string, $min = 0, $max = PHP_INT_MAX) {
        $length = mb_strlen($string, 'UTF-8');
        return $length >= $min && $length <= $max;
    }
    
    /**
     * Validate pattern
     * @param string $string
     * @param string $pattern
     * @return bool
     */
    public static function validatePattern($string, $pattern) {
        return preg_match($pattern, $string) === 1;
    }
    
    /**
     * Validate required fields
     * @param array $data
     * @param array $required
     * @return array
     */
    public static function validateRequired($data, $required) {
        $errors = [];
        
        foreach ($required as $field) {
            if (!isset($data[$field]) || empty(trim($data[$field]))) {
                $errors[] = "Field '{$field}' is required";
            }
        }
        
        return $errors;
    }
}
?>
